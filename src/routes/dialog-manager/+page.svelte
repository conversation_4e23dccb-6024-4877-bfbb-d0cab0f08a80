<script lang="ts">

  import type { SvelteComponent } from 'svelte';
  import MultipleInitialMenuItems from './examples/multipleInitialMenuItems.svelte';
  import NoMenuItems from './examples/noMenuItems.svelte';
  import DialogManagerExported from './examples/dialogManagerExported.svelte';
  import { ListItem, UnorderedList } from 'carbon-components-svelte';
  import '@tma/mediacenter-css/dist/css/mediacenter.css';

  type Examples = Record<string, {
      title: string;
      component: typeof SvelteComponent<any>;
    }>;

  enum anchorKeys {
    multipleInitialMenuItems = 'multiple-initial-menu-items',
    noInitialMenuItems = 'no-initial-menu-items',
    dialogManagerExported = 'dialog-manager-exported',
  };

  const prefix = 'studio-text/sandbox/dialog-manager/#';

  const examples: Examples = {
    [anchorKeys.multipleInitialMenuItems]: {
      title: 'Multiple Initial Menu Items',
      component: MultipleInitialMenuItems,
    },
    [anchorKeys.noInitialMenuItems]: {
      title: 'No Initial Menu Items',
      component: NoMenuItems,
    },
    [anchorKeys.dialogManagerExported]: {
      title: 'Dialog Manager Exported',
      component: DialogManagerExported,
    },
  };

</script>

<div id="main-content">
  <div id="content">
    <div>
      <h3>Dialog Manager Extension</h3>
      <div id="header-description">
        <p>
          This example demonstrates how to use the Dialog Manager extension. This extension allows you to
          add custom functionality to the editor. With the following props, you can customize the
          extension to meet your needs. For example you can add a custom component to the initial menu
          items, or add custom overflow menu items.
        </p>
      </div>
      {#each Object.entries(examples) as [_, example]}
        <svelte:component this={example.component} />
      {/each}
    </div>
  </div>
  <div id="sidebar-navigation">
    <h5>Examples</h5>
    <UnorderedList nested>
      {#each Object.entries(examples) as [key, value]}
        <ListItem><a href={prefix + key}>{value.title}</a></ListItem>
      {/each}
    </UnorderedList>
  </div>
</div>

<style lang="scss">
  #main-content {
    display: grid;
    grid-template-columns: 80% 20%;
    min-height: 100vh;
    @media (max-width: 1312px) {
      grid-template-columns: 70% 30%;
    }
    //hide the side bar when it gets too small
    @media (max-width: 672px) {
      grid-template-columns: 1fr;
    }
  }

  #content {
    min-width: 0;
    overflow: auto;
    display: flex;
    flex-direction: column;
    padding: 2rem 2rem 2rem 2rem;
    gap: 2rem;
  }

  #header-description {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  #sidebar-navigation {
    min-width: 0;
    background-color: var(--cds-ui-03, #ffffff);
    padding: 0 2rem;
    position: sticky;
    top: 0;
    height: 100vh;
    overflow-y: auto;
    //hide the side bar when it gets too small
    @media (max-width: 672px) {
      display: none;
    }
  }

  h3 {
    margin-bottom: 1rem;
  }

  h5 {
    margin: 2rem 0 1rem 0;
  }

  :global(.bx--list--unordered) {
    margin-left: 0;
  }
  :global(.bx--list__item::before) {
    list-style: none;
    display: none;
  }
  :global(.bx--list__item) {
    margin-bottom: 0.5rem;
    font-size: 15px;
  }
</style>