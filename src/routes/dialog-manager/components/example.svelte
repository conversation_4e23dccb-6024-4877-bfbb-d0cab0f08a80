<script lang="ts">
  export let selectedType: string | undefined = undefined;
  export let selectedMarkId: string | undefined = undefined;

  let count = 0;
</script>

<div>
  <p>
    This is an example of a component that can be passed in via a prop to the Dialog Manager
    extension slot.
  </p>
  <p>This component can be used to add custom functionality to the Dialog Manager extension.</p>
  <p>You can also have reactivity in this component.</p>
  {#if selectedType}
    <p>Selected type: {selectedType}</p>
  {/if}
  {#if selectedMarkId}
    <p>Selected mark id: {selectedMarkId}</p>
  {/if}
  <p>Count: {count}</p>
  <button type="button" on:click={() => count++}>Click me!</button>
</div>

<style lang="scss">
  p {
    margin: 0 0 0.5rem 0;
    font-size: 0.85rem;
  }
  button {
    font-size: 0.85rem;
    border: 0;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    &:hover {
      background-color: #eee;
    }
    &:active {
      background-color: #ddd;
    }
  }
</style>
