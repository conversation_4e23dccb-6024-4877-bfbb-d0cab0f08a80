<script lang="ts">
import { noMenuItemsMock } from '../mocks/noMenuItemsMock';
import Example from './example.svelte';
import TextEditor from '$lib/TextEditor.svelte';


const defaultVisibleFeatures = {
  strikethrough: { toolbar: true, bubbleMenu: true },
  bold: { toolbar: true, bubbleMenu: true },
  italicize: { toolbar: true, bubbleMenu: true },
  underline: { toolbar: true, bubbleMenu: true },
  VerticalMenu: { toolbar: true, bubbleMenu: true },
  subscript: { toolbar: true, bubbleMenu: true },
  superscript: { toolbar: true, bubbleMenu: true },
  HighlightMenu: { toolbar: true, bubbleMenu: true },
  TextColorMenu: { toolbar: true, bubbleMenu: true },
  textColor: { toolbar: false, bubbleMenu: false },
  alignmentAll: { toolbar: true, bubbleMenu: true },
  alignLeft: { toolbar: true, bubbleMenu: true },
  alignRight: { toolbar: true, bubbleMenu: true },
  alignCenter: { toolbar: true, bubbleMenu: true },
  alignJustify: { toolbar: true, bubbleMenu: true },
  TypographyMenu: { toolbar: true, bubbleMenu: true },
  orderedList: { toolbar: false, bubbleMenu: false },
  bulletList: { toolbar: false, bubbleMenu: false },
  insertLink: { toolbar: true, bubbleMenu: true },
  highlight: { toolbar: false, bubbleMenu: false },
  proofReader: { toolbar: true, bubbleMenu: true },
  infoDialog: { toolbar: false, bubbleMenu: false },
  fontZoom: { statusbar: false },
  fullScreen: { statusbar: false },
};

</script>

<h5>Component passed into Menu Item</h5>
<p>
  This example demonstrates the functionality when no initial menu items are provided. In this case,
  the onClick function is called directly. Since, there is only one menu item, the initial menu is
  not displayed.
</p>

<TextEditor
  html={noMenuItemsMock}
  features={{
      ...defaultVisibleFeatures,
      DialogManager: {
        bubbleMenu: true,
        options: {
          title: 'Dialog Title',
          initialMenuItems: [
            {
              text: 'Add from source...',
              hasDivider: true,
              type: 'addFromSource',
              component: Example,
              onClick: () => {
                console.log('Custom Initial Menu Item Clicked');
              },
            },
          ],
          onMarkAdded: (event) => {
            if (!event) {
              return;
            }

            console.log('DP Content Action', event);
          },
        },
      },
    }}
/>


<style lang="scss">
  p {
    margin: 0 0 0.5rem 0;
  }
  h5 {
    margin: 2rem 0 0.5rem 0;
  }
</style>