<script lang="ts">
  import ExtensionDialogManagerComponent from '$lib/Extensions/extension-dialog-manager/extension-dialog-manager.component.svelte'
  import Example from '../components/example.svelte';
  import { Button } from 'carbon-components-svelte';

  let showComponent = false;

  function handleButtonClick() {
    showComponent = !showComponent;
  }

</script>

<Button on:click={handleButtonClick}>
  {#if showComponent}
    Close
  {:else}
    Open
  {/if}
</Button>

{#if showComponent}
  <ExtensionDialogManagerComponent
    title="Dialog Title"
    borderColor="#2BAC66"
    backgroundColor="#E6FDE5"
    overflowItems={[
      { text: 'Item 1', onClick: () => console.log('Item 1 clicked') },
      { text: 'Item 2', onClick: () => console.log('Item 2 clicked') },
    ]}
    initialMenuItems={[
      {
        text: 'Add from source...',
        hasDivider: true,
        type: 'addFromSource',
        onClick: () => {
          console.log('Custom Initial Menu Item Clicked');
        },
      },
    ]}
    selectedType="addFromSource"
    selectedMarkId="12345"
    component={Example}
    showMenu={false}
    onClose={() => (showComponent = false)}
  />
{/if}
