<script>
import Example from './example.svelte';
import { digitalDialogMock } from '../mocks/digitalDialogMock';
import TextEditor from '$lib/TextEditor.svelte';

const defaultVisibleFeatures = {
  strikethrough: { toolbar: true, bubbleMenu: true },
  bold: { toolbar: true, bubbleMenu: true },
  italicize: { toolbar: true, bubbleMenu: true },
  underline: { toolbar: true, bubbleMenu: true },
  VerticalMenu: { toolbar: true, bubbleMenu: true },
  subscript: { toolbar: true, bubbleMenu: true },
  superscript: { toolbar: true, bubbleMenu: true },
  HighlightMenu: { toolbar: true, bubbleMenu: true },
  TextColorMenu: { toolbar: true, bubbleMenu: true },
  textColor: { toolbar: false, bubbleMenu: false },
  alignmentAll: { toolbar: true, bubbleMenu: true },
  alignLeft: { toolbar: true, bubbleMenu: true },
  alignRight: { toolbar: true, bubbleMenu: true },
  alignCenter: { toolbar: true, bubbleMenu: true },
  alignJustify: { toolbar: true, bubbleMenu: true },
  TypographyMenu: { toolbar: true, bubbleMenu: true },
  orderedList: { toolbar: false, bubbleMenu: false },
  bulletList: { toolbar: false, bubbleMenu: false },
  insertLink: { toolbar: true, bubbleMenu: true },
  highlight: { toolbar: false, bubbleMenu: false },
  proofReader: { toolbar: true, bubbleMenu: true },
  infoDialog: { toolbar: false, bubbleMenu: false },
  fontZoom: { statusbar: false },
  fullScreen: { statusbar: false },
};

</script>

<TextEditor
  features={{
      ...defaultVisibleFeatures,
      fullScreen: { statusbar: true },
      fontZoom: { statusbar: true },
      DialogManager: {
        bubbleMenu: true,
        options: {
          title: 'Dialog Title',
          initialMenuItems: [
            {
              text: 'Add from source...',
              hasDivider: true,
              type: 'addFromSource',
              component: Example,
              onClick: () => {
                console.log('Custom Initial Menu Item Clicked');
              },
            },
            {
              text: 'Not a scripture',
              type: 'ignoreScripture',
              onClick: () => {
                console.log('ignoreScripture custom onclick fucntion');
              },
            },
            {
              text: 'Not a publication',
              type: 'ignorePubRef',
              component: Example,
              onClick: () => {
                console.log('Custom Initial Menu Item Clicked');
              },
            },
            {
              text: 'Do not index',
              type: 'ignoreIndexing',
              onClick: () => {
                console.log('Custom Initial Menu Item Clicked');
              },
            },
          ],
          overflowMenuItems: [
            {
              text: 'Advanced',
              onClick: () => {
                console.log('Custom Overflow Menu Item Clicked');
              },
            },
            {
              text: 'Edit',
              onClick: () => {
                console.log('Custom Overflow Menu Item Clicked');
              },
            },
          ],
          onMarkAdded: (event) => {
            if (!event) {
              return;
            }

            console.log('DP Content Action', event);
          },
        },
      },
    }}
  html={digitalDialogMock}
  height="400px" />