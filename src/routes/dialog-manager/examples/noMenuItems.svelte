<script lang="ts">
  import NoMenuItems from '../components/noMenuItems.svelte';
  import NoMenuItemsRaw from '../components/noMenuItems.svelte?raw';
  import NoMenuItemsWithComponent from '../components/noMenuItemsWithComponent.svelte';
  import NoMenuItemsWithComponentRaw from '../components/noMenuItemsWithComponent.svelte?raw';
  import CodePreview from '../../../components/codePreview.svelte';
</script>

<div id="rich-content-marker">

  <h4>No Initial Menu Items</h4>

  <p>
    This example demonstrates the functionality when no initial menu items are provided. There are two
    scenarios that will occur. If the one menu item has no component being passed, it will directly call
    the onClick function. If the one menu item has a component being passed, it will render the
    component automatically.
  </p>

  <p>
    In both of these scenarios the overflow menu will not be displayed.
  </p>

  <NoMenuItems />

  <CodePreview rawCode={NoMenuItemsRaw} />

  <NoMenuItemsWithComponent />

  <CodePreview rawCode={NoMenuItemsWithComponentRaw} />

</div>

<style>
    #rich-content-marker {
        padding: 2rem 0;
    }
    #rich-content-marker p {
        margin: 1rem 0;
        font-size: 1rem;
    }

    #rich-content-marker :global(.bx--data-table) {
        width: 100%;
        margin-bottom: 2rem;
    }

</style>
