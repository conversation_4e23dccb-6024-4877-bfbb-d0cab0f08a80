<script lang="ts">
  import DialogManagerExported from '../components/dialogManagerExported.svelte';
  import DialogManagerExportedRaw from '../components/dialogManagerExported.svelte?raw';
  import CodePreview from '../../../components/codePreview.svelte';

</script>

<div id="rich-content-marker">

  <h4>Use Component Exported from Dialog Manager</h4>

  <p>
    This example demonstrates how to use the component that is exported from the Dialog Manager
    extension. This component can be used to add custom functionality to the Dialog Manager extension.
  </p>

  <DialogManagerExported />

  <CodePreview rawCode={DialogManagerExportedRaw} />

</div>

<style>
    #rich-content-marker {
        padding: 2rem 0;
    }
    #rich-content-marker p {
        margin: 1rem 0;
        font-size: 1rem;
    }

    #rich-content-marker :global(.bx--data-table) {
        width: 100%;
        margin-bottom: 2rem;
    }

</style>
