<script lang="ts">
  import MultipleMenuItems from '../components/multipleMenuItems.svelte'
  import MultipleMenuItemsRaw from '../components/multipleMenuItems.svelte?raw';
  import CodePreview from '../../../components/codePreview.svelte';
  import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from 'carbon-components-svelte';

</script>

<div id="rich-content-marker">

  <h4>Multiple Initial Menu Items</h4>

  <p>
    This example demonstrates how to use the <code>initialMenuItems</code> prop to add multiple
    initial menu items to the Dialog Manager extension.
  </p>

  <p>
    Some of the options available for this feature include:
  </p>
  <Table size="short">
    <TableHead>
      <TableRow>
        <TableHeader>Option</TableHeader>
        <TableHeader>Description</TableHeader>
      </TableRow>
    </TableHead>
    <TableBody>
      <TableRow>
        <TableCell>text</TableCell>
        <TableCell>The text to display for the menu item</TableCell>
      </TableRow>
      <TableRow>
        <TableCell>type</TableCell>
        <TableCell>The type of the menu item</TableCell>
      </TableRow>
      <TableRow>
        <TableCell>onClick</TableCell>
        <TableCell>The function to call when the menu item is clicked</TableCell>
      </TableRow>
      <TableRow>
        <TableCell>component</TableCell>
        <TableCell>The component to render when the menu item is clicked</TableCell>
      </TableRow>
      <TableRow>
        <TableCell>hasDivider</TableCell>
        <TableCell>Whether to display a divider after the menu item</TableCell>
      </TableRow>
    </TableBody>
  </Table>

  <MultipleMenuItems />

  <CodePreview rawCode={MultipleMenuItemsRaw} />

</div>

<style>
    #rich-content-marker {
        padding: 2rem 0;
    }
    #rich-content-marker p {
        margin: 1rem 0;
        font-size: 1rem;
    }

    #rich-content-marker :global(.bx--data-table) {
        width: 100%;
        margin-bottom: 2rem;
    }

</style>
