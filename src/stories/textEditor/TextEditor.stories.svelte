<script lang="ts" context="module">
  import { Template, Story } from '@storybook/addon-svelte-csf';
  import TextEditor from '$lib/TextEditor.svelte';
  import type { ComponentProps } from 'svelte';
  import type { Meta } from '@storybook/svelte';
  import { jsonContentWithAutomarkupAndComments } from '../../routes/mocks/automarkup';

  const defaultArgs: ComponentProps<TextEditor> = {
    html: '<p>examples of HTML content, with <readonly>Read Only</readonly> content.</p>',
    features: {
      strikethrough: { toolbar: true, bubbleMenu: true },
      bold: { toolbar: true, bubbleMenu: true },
      italicize: { toolbar: true, bubbleMenu: true },
      underline: { toolbar: true, bubbleMenu: true },
      VerticalMenu: { toolbar: true, bubbleMenu: true },
      subscript: { toolbar: true, bubbleMenu: true },
      superscript: { toolbar: true, bubbleMenu: true },
      HighlightMenu: { toolbar: true, bubbleMenu: true },
      TextColorMenu: { toolbar: true, bubbleMenu: true },
      textColor: { toolbar: false, bubbleMenu: false },
      alignmentAll: { toolbar: true, bubbleMenu: true },
      alignLeft: { toolbar: true, bubbleMenu: true },
      alignRight: { toolbar: true, bubbleMenu: true },
      alignCenter: { toolbar: true, bubbleMenu: true },
      alignJustify: { toolbar: true, bubbleMenu: true },
      TypographyMenu: { toolbar: true, bubbleMenu: true },
      orderedList: { toolbar: false, bubbleMenu: false },
      bulletList: { toolbar: false, bubbleMenu: false },
      insertLink: { toolbar: true, bubbleMenu: true },
      highlight: { toolbar: false, bubbleMenu: false },
      proofReader: { toolbar: true, bubbleMenu: true },
      infoDialog: { toolbar: false, bubbleMenu: false },
      fontZoom: { statusbar: true },
      fullScreen: { statusbar: true },
    },
    showStatusBarOnFocus: true,
  };

  export const meta: Meta = {
    title: 'TextEditor',
    component: TextEditor,
    tags: ['autodocs'],
    args: defaultArgs,
  };
</script>

<Template let:args>
  <TextEditor on:blur on:change on:focus on:selectionUpdate {...args} />
</Template>

<Story name="Default" />
<Story
  name="Bubble Menu only"
  args={{
    html: '<p>Bubble Menu only.</p>',
    features: {
      strikethrough: { toolbar: false, bubbleMenu: true },
      bold: { toolbar: false, bubbleMenu: true },
      italicize: { toolbar: false, bubbleMenu: true },
      underline: { toolbar: false, bubbleMenu: true },
      VerticalMenu: { toolbar: false, bubbleMenu: true },
      subscript: { toolbar: false, bubbleMenu: true },
      superscript: { toolbar: false, bubbleMenu: true },
      HighlightMenu: { toolbar: false, bubbleMenu: true },
      TextColorMenu: { toolbar: false, bubbleMenu: true },
      textColor: { toolbar: false, bubbleMenu: false },
      alignmentAll: { toolbar: false, bubbleMenu: true },
      alignLeft: { toolbar: false, bubbleMenu: true },
      alignRight: { toolbar: false, bubbleMenu: true },
      alignCenter: { toolbar: false, bubbleMenu: true },
      alignJustify: { toolbar: false, bubbleMenu: true },
      TypographyMenu: { toolbar: false, bubbleMenu: true },
      orderedList: { toolbar: false, bubbleMenu: false },
      bulletList: { toolbar: false, bubbleMenu: false },
      insertLink: { toolbar: false, bubbleMenu: true },
      highlight: { toolbar: false, bubbleMenu: false },
      proofReader: { toolbar: false, bubbleMenu: true },
      infoDialog: { toolbar: false, bubbleMenu: false },
      ReadOnly: { toolbar: false, bubbleMenu: true },
      fontZoom: { statusbar: true },
      fullScreen: { statusbar: true },
    },
    showStatusBarOnFocus: false,
  }} />
<Story
  name="Auto Show/Hide Statusbar"
  args={{
    ...defaultArgs,
    showStatusBarOnFocus: true,
  }} />
<Story
  name="BibleVerse"
  args={{
    ...defaultArgs,
    html: '<p><readonly><num>1</num></readonly> In the beginning God created the heavens and the earth.<readonly><sc>a</sc></readonly></p><p><readonly><strong><span style="color: #4a6da7;">2</span></strong></readonly> Now the earth was formless and desolate,<readonly><sup><span style="color: #4a6da7;">*</span></sup></readonly> and there was darkness upon the surface of the watery deep,<readonly><sup><span style="color: #4a6da7;">#</span></sup></readonly> <readonly><sup><span style="color: #4a6da7;">b</span></sup></readonly> and God’s active force <readonly><sup><span style="color: #4a6da7;">c</span></sup></readonly> was moving about over the surface of the waters.<readonly><sup><span style="color: #4a6da7;">d</span></sup></readonly></p><p><readonly><strong><span style="color: #4a6da7;">3</span></strong></readonly> And God said: “Let there be light.” Then there was light. <readonly><sup><span style="color: #4a6da7;">e</span></sup></readonly> <readonly><strong><span style="color: #4a6da7;">4</span></strong></readonly> After that God saw that the light was good, and God began to divide the light from the darkness. <readonly><strong><span style="color: #4a6da7;">5</span></strong></readonly> God called the light Day, but the darkness he called Night. <readonly><sup><span style="color: #4a6da7;">f</span></sup></readonly> And there was evening and there was morning, a first day.</p>',
  }} />
<Story
  name="Search And Replace"
  args={{
    ...defaultArgs,
    html: '<p>Minimal <strong>It</strong>ems.</p><p>Hello world my name is Abram, but soon it will be Abraham</p><p><readonly><num>1</num></readonly> In the beginning God created the heavens and the earth.<readonly><sc>a</sc></readonly></p><p><readonly><strong><span style="color: #4a6da7;">2</span></strong></readonly> Now the earth was formless and desolate,<readonly><sup><span style="color: #4a6da7;">*</span></sup></readonly> and there was darkness upon the surface of the watery deep,<readonly><sup><span style="color: #4a6da7;">#</span></sup></readonly> <readonly><sup><span style="color: #4a6da7;">b</span></sup></readonly> and God’s active force <readonly><sup><span style="color: #4a6da7;">c</span></sup></readonly> was moving about over the surface of the waters.<readonly><sup><span style="color: #4a6da7;">d</span></sup></readonly></p><p><readonly><strong><span style="color: #4a6da7;">3</span></strong></readonly> And God said: “Let there be light.” Then there was light. <readonly><sup><span style="color: #4a6da7;">e</span></sup></readonly> <readonly><strong><span style="color: #4a6da7;">4</span></strong></readonly> After that God saw that the light was good, and God began to divide the light from the darkness. <readonly><strong><span style="color: #4a6da7;">5</span></strong></readonly> God called the light Day, but the darkness he called Night. <readonly><sup><span style="color: #4a6da7;">f</span></sup></readonly> And there was evening and there was morning, a first day.</p>',
    features: {
      ...defaultArgs.features,
      searchReplace: { toolbar: true, statusbar: true, bubbleMenu: false },
    },
  }} />
<Story
  name="Search Only"
  args={{
    ...defaultArgs,
    html: '<p>Minimal <strong>It</strong>ems.</p><p>Hello world my name is Abram, but soon it will be Abraham</p><p><readonly><num>1</num></readonly> In the beginning God created the heavens and the earth.<readonly><sc>a</sc></readonly></p><p><readonly><strong><span style="color: #4a6da7;">2</span></strong></readonly> Now the earth was formless and desolate,<readonly><sup><span style="color: #4a6da7;">*</span></sup></readonly> and there was darkness upon the surface of the watery deep,<readonly><sup><span style="color: #4a6da7;">#</span></sup></readonly> <readonly><sup><span style="color: #4a6da7;">b</span></sup></readonly> and God’s active force <readonly><sup><span style="color: #4a6da7;">c</span></sup></readonly> was moving about over the surface of the waters.<readonly><sup><span style="color: #4a6da7;">d</span></sup></readonly></p><p><readonly><strong><span style="color: #4a6da7;">3</span></strong></readonly> And God said: “Let there be light.” Then there was light. <readonly><sup><span style="color: #4a6da7;">e</span></sup></readonly> <readonly><strong><span style="color: #4a6da7;">4</span></strong></readonly> After that God saw that the light was good, and God began to divide the light from the darkness. <readonly><strong><span style="color: #4a6da7;">5</span></strong></readonly> God called the light Day, but the darkness he called Night. <readonly><sup><span style="color: #4a6da7;">f</span></sup></readonly> And there was evening and there was morning, a first day.</p>',
    features: {
      ...defaultArgs.features,
      searchReplace: {
        toolbar: true,
        statusbar: true,
        bubbleMenu: false,
        options: { searchOnly: true },
      },
    },
  }} />
<Story
  name="Automarkup & comments"
  args={{
    ...defaultArgs,
    html: jsonContentWithAutomarkupAndComments,
    features: {
      ...defaultArgs.features,
      fullScreen: { statusbar: true },
      fontZoom: { statusbar: true },
      searchReplace: {
        toolbar: true,
        statusbar: true,
        bubbleMenu: false,
        options: { searchOnly: false },
      },
    },
    height: '800px',
  }} />
