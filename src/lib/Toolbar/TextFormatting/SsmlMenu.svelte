<script lang="ts">
  import Tag from 'carbon-icons-svelte/lib/Tag.svelte';
  import { onMount, onDestroy, createEventDispatcher } from 'svelte';
  import type { Editor, JSONContent } from '@tiptap/core';
  import { Button } from 'carbon-components-svelte';
  import { getDefaultShortcuts } from './shortcuts';
  import { Feature } from '$lib/types';
  import BreakModal from '../../ssml/modals/breakModal.svelte';
  import EmphasisModal from '../../ssml/modals/emphasisModal.svelte';
  import PhonemeModal from '../../ssml/modals/phonemeModal.svelte';
  import ProsodyModal from '../../ssml/modals/prosodyModal.svelte';
  import SayasModal from '../../ssml/modals/sayasModal.svelte';
  import SilentModal from '../../ssml/modals/silentModal.svelte';
  import SubstituteModal from '../../ssml/modals/substituteModal.svelte';
  import DeleteTagsModal from '../../ssml/modals/deleteTagsModal.svelte';
  import Portal from 'svelte-portal';

  export let editor: Editor;
  export let shortcutsSub: string[] = getDefaultShortcuts(Feature.Subscript);
  export let size: 'default' | 'small' = 'default';
  export let tooltipPosition: 'top' | 'bottom' = 'top';
  export let languageCode = 'E';
  export let shortcuts = getDefaultShortcuts(Feature.Ssml);

  getDefaultShortcuts(Feature.Superscript);

  const dispatch = createEventDispatcher();
  const ssmlMarkTypeNames = ['emphasis', 'phoneme', 'prosody', 'sayas', 'silent', 'substitute'];
  const audioPlayerSourceHeader = 'data:audio/ogg;base64';

  let isActive = false;

  let openBreakModal = false;
  let openDeleteTagsModal = false;
  let openEmphasisModal = false;
  let openPhonemeModal = false;
  let openProsodyModal = false;
  let openSayasModal = false;
  let openSubstituteModal = false;
  let openSilentModal = false;

  let textSelected = '';
  let textBeforeSelected = '';
  let textAfterSelected = '';
  let isTextSelected = false;
  let containsSSMLTags = false;

  const audioText = '';

  function handleClick(event: MouseEvent) {
    getSelectedText();
    checkIfThereIsTextSelected();
    containsSSMLTags = checkIfContainsSsmlTags();
    isActive = !isActive;
    dispatch('click', event);
  }

  function handleClickOutside(event: MouseEvent) {
    // Check if the click is outside the button
    if (event.target instanceof Element && !event.target.closest('.ssml-dropdown')) {
      isActive = false;
    }
  }

  onMount(() => {
    // Add event listener when component is mounted
    document.addEventListener('click', handleClickOutside);
  });

  onDestroy(() => {
    // Remove event listener when component is destroyed
    document.removeEventListener('click', handleClickOutside);
  });

  let isTagsHidden = false; // Local state to track the visibility of tags

  function removeNodesByType(jsonContent: JSONContent, typeName: string) {
    if (jsonContent.content) {
      if (jsonContent.content.some((n) => n.type === typeName)) {
        jsonContent.content = jsonContent.content.filter((n) => n.type !== typeName);
      }

      jsonContent.content.forEach((element) => {
        removeNodesByType(element, typeName);
      });
    }
  }

  function removeSSMLBreaks(editor: Editor) {
    const jsonContent = editor.getJSON();
    removeNodesByType(jsonContent, 'break');
    return jsonContent;
  }

  function toggleTags() {
    if (editor) {
      const hasClass = editor.view.dom.classList.toggle('hidden-tags');
      isTagsHidden = hasClass; // Update state based on class presence
    }
  }

  function deleteAllSSMLTags(): void {
    if (editor) {
      editor.commands.selectAll();
      ssmlMarkTypeNames.forEach((m) => editor.commands.unsetMark(m));
      const jsonNoBreaksNoSSMLMarks = removeSSMLBreaks(editor);

      // To run command setContent first remove any mark from document, if not that command does't works :)
      editor.commands.selectAll();
      editor.commands.unsetAllMarks();
      editor.commands.setContent(jsonNoBreaksNoSSMLMarks);

      editor.chain().focus().setTextSelection(0).run();
    }
  }

  function getSelectedText(): void {
    if (editor) {
      const { from, to, empty } = editor.state.selection;
      if (empty) {
        textSelected = '';
        textBeforeSelected = '';
        textAfterSelected = '';
      }
      textSelected = editor.state.doc.textBetween(from, to, ' ');
      textBeforeSelected = editor.state.doc.textBetween(from - 20 > 0 ? from - 20 : 0, from);
      textAfterSelected = editor.state.doc.textBetween(
        to,
        to + 20 <= editor.getText().length ? to + 20 : editor.getText().length
      );
    }
  }

  function checkIfThereIsTextSelected(): void {
    isTextSelected = textSelected.trim().length > 0;
  }

  function checkIfContainsSsmlTags() {
    let result = false;
    if (editor) {
      editor.state.doc.descendants((node) => {
        if (
          (!result &&
            node.type.name === 'text' &&
            node.marks.length > 0 &&
            ssmlMarkTypeNames.includes(node.marks[0].type.name)) ||
          node.type.name === 'break'
        ) {
          result = true;
        }
      });
    }

    return result;
  }

  function deleteSelectedMarks() {
    if (editor) {
      ssmlMarkTypeNames.forEach((m) => editor.commands.unsetMark(m));
      /*       const jsonNoBreaksNoSSMLMarks = removeSSMLBreaks(editor); */

      // To run command setContent first remove any mark from document, if not that command does't works :)
      /*       editor.commands.selectAll();
      editor.commands.unsetAllMarks();
      editor.commands.setContent(jsonNoBreaksNoSSMLMarks);

      editor.chain().focus().setTextSelection(0).run(); */
    }
  }
</script>

<div class="ssml-dropdown" class:active={isActive}>
  <Button
    kind="ghost"
    iconDescription={`SSML Tags`}
    {tooltipPosition}
    icon={Tag}
    disabled={!editor.isEditable}
    {size}
    on:click={handleClick} />
  <div>
    <Button
      kind="ghost"
      class="button-disabled-{isTextSelected}"
      disabled={isTextSelected}
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      on:click={() => (openBreakModal = true)}>
      Break
    </Button>
    <Button
      kind="ghost"
      class="button-disabled-{!isTextSelected}"
      disabled={!isTextSelected}
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      on:click={() => {
        getSelectedText();
        openEmphasisModal = true;
      }}>
      Emphasis
    </Button>
    <Button
      kind="ghost"
      class="button-disabled-{!isTextSelected}"
      disabled={!isTextSelected}
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      on:click={() => {
        getSelectedText();
        openPhonemeModal = true;
      }}>
      Phoneme
    </Button>
    <Button
      kind="ghost"
      class="button-disabled-{!isTextSelected}"
      disabled={!isTextSelected}
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      on:click={() => {
        getSelectedText();
        openProsodyModal = true;
      }}>
      Prosody
    </Button>
    <!--     <Button
      kind="ghost"
      class="button-disabled-{!isTextSelected}"
      disabled={!isTextSelected}
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      on:click={() => {
        getSelectedText();
        openSayasModal = true;
      }}>
      Say-as
    </Button> -->
    <!--     <Button
      kind="ghost"
      class="button-disabled-{!isTextSelected}"
      disabled={!isTextSelected}
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      on:click={() => {
        getSelectedText();
        openSilentModal = true;
      }}>
      Silent
    </Button> -->
    <Button
      kind="ghost"
      class="button-disabled-{!isTextSelected}"
      disabled={!isTextSelected}
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      on:click={() => {
        getSelectedText();
        openSubstituteModal = true;
      }}>
      Substitute
    </Button>
    <Button
      kind="ghost"
      class="button-disabled-{!containsSSMLTags}"
      iconDescription={`Subscript (${shortcutsSub[0]})`}
      {tooltipPosition}
      {size}
      disabled={!containsSSMLTags}
      on:click={(event) => {
        toggleTags(); // Toggle the "hidden-tags" class
        handleClick(event);
      }}>
      {isTagsHidden ? 'Show Tags' : 'Hide Tags'}
    </Button>
    <hr />
    {#if textSelected === ''}
      <Button
        kind="ghost"
        class="button-disabled-{!containsSSMLTags}"
        disabled={!containsSSMLTags}
        iconDescription={`Subscript (${shortcutsSub[0]})`}
        {tooltipPosition}
        {size}
        on:click={() => {
          openDeleteTagsModal = true;
        }}>
        Delete All
      </Button>
    {:else}
      <Button
        kind="ghost"
        class="button-disabled-{!containsSSMLTags}"
        disabled={!containsSSMLTags}
        iconDescription={`Subscript (${shortcutsSub[0]})`}
        {tooltipPosition}
        {size}
        on:click={() => {
          deleteSelectedMarks();
        }}>
        Delete Selected Tags.
      </Button>
    {/if}
  </div>
</div>

<div class="audio-player">
  <audio controls autoplay src={audioText} />
</div>

{#if openBreakModal}
  <Portal>
    <BreakModal
      {languageCode}
      bind:openBreakModal
      textBefore={textBeforeSelected}
      textAfter={textAfterSelected}
      {editor} />
  </Portal>
{/if}

{#if openEmphasisModal}
  <Portal>
    <EmphasisModal {languageCode} text={textSelected} level="" bind:openEmphasisModal {editor} />
  </Portal>
{/if}

{#if openPhonemeModal}
  <Portal>
    <PhonemeModal {languageCode} text={textSelected} ph="" bind:openPhonemeModal {editor} />
  </Portal>
{/if}

{#if openProsodyModal}
  <Portal>
    <ProsodyModal {languageCode} text={textSelected} bind:openProsodyModal {editor} />
  </Portal>
{/if}

{#if openSayasModal}
  <Portal>
    <SayasModal {languageCode} text={textSelected} interpretAs="" bind:openSayasModal {editor} />
  </Portal>
{/if}

{#if openSilentModal}
  <Portal>
    <SilentModal {languageCode} text={textSelected} silentText="" bind:openSilentModal {editor} />
  </Portal>
{/if}

{#if openSubstituteModal}
  <Portal>
    <SubstituteModal
      {languageCode}
      text={textSelected}
      alias=""
      bind:openSubstituteModal
      {editor} />
  </Portal>
{/if}

{#if openDeleteTagsModal}
  <Portal>
    <DeleteTagsModal bind:openDeleteTagsModal on:onConfirm={deleteAllSSMLTags} />
  </Portal>
{/if}

<style>
  .ssml-dropdown {
    position: relative;
    z-index: 100;
  }
  .ssml-dropdown > div {
    display: none;
    position: absolute;
    bottom: 0;
    left: 0;
    transform: translateY(100%);
    background-color: #fff;
  }
  .ssml-dropdown.active > div {
    display: block;
    min-width: 120px;
    padding: 6px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    text-align: left;
  }
  .ssml-dropdown.active > div :global(.bx--btn) {
    display: block !important;
    width: 100%;
    padding: 2px 10px;
    min-height: auto;
    text-align: left;
  }
  :global(.button-disabled-true) {
    color: #8d8d8d !important;
  }
  :global(.tiptap .text-editor-ssml-break) {
    display: inline-block;
    position: relative;
  }
  :global(.tiptap .text-editor-ssml-break:before) {
    content: '<break>';
    font-size: 80%;
    position: relative;
    color: #740937;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }

  :global(.tiptap .text-editor-ssml-break:hover::before) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }

  :global(.tiptap .text-editor-ssml-emphasis) {
    position: relative;
    line-height: inherit;
    font-size: 60%;
  }
  :global(.tiptap .text-editor-ssml-emphasis:before) {
    content: '<emphasis>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #004144;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-emphasis:hover::before),
  :global(.tiptap [class^='text-editor-ssml-'].selected::before),
  :global(.tiptap [class^='text-editor-ssml-'].selected::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }

  /*   :global(.tiptap .text-editor-ssml-emphasis:hover::before),
  :global(.tiptap [class='text-editor-ssml-emphasis'].active::before),
  :global(.tiptap [class='text-editor-ssml-emphasis'].active::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  } */

  :global(.tiptap .text-editor-ssml-emphasis:after) {
    content: '</emphasis>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #004144;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-emphasis:hover::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-phoneme) {
    position: relative;
  }
  :global(.tiptap .text-editor-ssml-phoneme:before) {
    content: '<phoneme>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #044317;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-phoneme:hover::before) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-phoneme:after) {
    content: '</phoneme>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #044317;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-phoneme:hover::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-prosody) {
    position: relative;
  }
  :global(.tiptap .text-editor-ssml-prosody:before) {
    content: '<prosody>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #491d8b;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-prosody:hover::before) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-prosody:after) {
    content: '</prosody>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #491d8b;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-prosody:hover::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-sayas) {
    position: relative;
  }
  :global(.tiptap .text-editor-ssml-sayas:before) {
    content: '<say-as>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #003a6d;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-sayas:hover::before) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-sayas:after) {
    content: '</say-as>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #003a6d;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-sayas:hover::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-sub) {
    position: relative;
    vertical-align: middle;
  }
  :global(.tiptap .text-editor-ssml-sub:before) {
    content: '<substitute>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #393939;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-sub:hover::before) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-sub:after) {
    content: '</substitute>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #393939;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-sub:hover::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-silent) {
    position: relative;
    vertical-align: middle;
  }
  :global(.tiptap .text-editor-ssml-silent:before) {
    content: '<silent>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #750e13;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-silent:hover::before) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }
  :global(.tiptap .text-editor-ssml-silent:after) {
    content: '</silent>';
    font-size: 80%;
    font-style: italic;
    position: relative;
    color: #750e13;
    padding: 2px 5px;
    vertical-align: middle;
    margin: 0 2px 0;
    display: inline-block;
    background-color: #f8f8f8;
  }
  :global(.tiptap .text-editor-ssml-silent:hover::after) {
    background-color: var(--cds-background-selected-hover);
    cursor: pointer;
  }

  .ssml-dropdown div {
    width: 170px;
  }
  .audio-player {
    display: none;
  }
  :global(.tiptap.hidden-tags .text-editor-ssml-break:before),
  :global(.tiptap.hidden-tags .text-editor-ssml-emphasis:before),
  :global(.tiptap.hidden-tags .text-editor-ssml-emphasis:after),
  :global(.tiptap.hidden-tags .text-editor-ssml-phoneme:before),
  :global(.tiptap.hidden-tags .text-editor-ssml-phoneme:after),
  :global(.tiptap.hidden-tags .text-editor-ssml-prosody:before),
  :global(.tiptap.hidden-tags .text-editor-ssml-prosody:after),
  :global(.tiptap.hidden-tags .text-editor-ssml-sayas:before),
  :global(.tiptap.hidden-tags .text-editor-ssml-sayas:after),
  :global(.tiptap.hidden-tags .text-editor-ssml-sub:before),
  :global(.tiptap.hidden-tags .text-editor-ssml-sub:after) {
    display: none;
  }
</style>
