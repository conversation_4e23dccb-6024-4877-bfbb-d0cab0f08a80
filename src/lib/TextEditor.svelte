<script lang="ts">
  import type {
    AnyExtension,
    EditorOptions,
    J<PERSON>NContent,
    KeyboardShortcutCommand,
  } from '@tiptap/core';
  import { Editor, generateHTML, generateJSON, getExtensionField } from '@tiptap/core';
  import { Document } from '@tiptap/extension-document';
  import { Paragraph } from '@tiptap/extension-paragraph';
  import { Text } from '@tiptap/extension-text';
  import { History } from '@tiptap/extension-history';
  import { Bold } from '@tiptap/extension-bold';
  import { BulletList } from '@tiptap/extension-bullet-list';
  import { Highlight } from '@tiptap/extension-highlight';
  import { Italic } from '@tiptap/extension-italic';
  import { Link } from '@tiptap/extension-link';
  import { ListItem } from '@tiptap/extension-list-item';
  import { OrderedList } from '@tiptap/extension-ordered-list';
  import { Strike } from '@tiptap/extension-strike';
  import { Subscript } from '@tiptap/extension-subscript';
  import { Superscript } from '@tiptap/extension-superscript';
  import { TextAlign } from '@tiptap/extension-text-align';
  import { TextStyle } from '@tiptap/extension-text-style';
  import { Underline } from '@tiptap/extension-underline';
  import BubbleMenuExtension from '@tiptap/extension-bubble-menu';
  import TableFeature from '@tiptap/extension-table';
  import TableRow from '@tiptap/extension-table-row';
  import { TableCell } from '@tiptap/extension-table-cell';
  import TableHeader from '@tiptap/extension-table-header';
  import type { Transaction } from 'prosemirror-state';
  import { createEventDispatcher, onDestroy, onMount, setContext, getContext } from 'svelte';
  import { FontFamily } from '$lib/Toolbar/TextFormatting/Extensions/FontFamily';
  import { FontSize } from '$lib/Toolbar/TextFormatting/Extensions/FontSize';
  import { TextColor } from '$lib/Toolbar/TextFormatting/Extensions/TextColor';
  import { TextDirection } from '$lib/Toolbar/TextFormatting/Extensions/TextDirection';
  import { getDefaultShortcuts } from '$lib/Toolbar/TextFormatting/shortcuts';
  import { Color } from '@tiptap/extension-color';
  import Toolbar from '$lib/Toolbar/Toolbar.svelte';
  import Statusbar from './Statusbar/Statusbar.svelte';
  import BubbleMenu from '$lib/Toolbar/BubbleMenu.svelte';
  import TrackChanges from '$lib/Extensions/trackChanges';
  import { autolink } from './autolink';
  import type {
    FeatureConfiguration,
    ParseOptions,
    PortalTarget,
    EventHandlers,
    Language,
    DictionaryCallback,
    TextEditorContext,
    DropdownItems,
  } from './types';
  import { Feature } from './types';
  import { clickHandler } from './clickHandler';
  import Portal, { portal } from 'svelte-portal';
  import { Modal } from 'carbon-components-svelte';
  import { Heading } from '@tiptap/extension-heading';

  import { ProofreaderExtension } from '$lib/Extensions/Proofreader/Proofreader.extension';
  import { DialogManagerExtension } from '$lib/Extensions/extension-dialog-manager/extension-dialog-manager.extension';
  import { writable } from 'svelte/store';
  import type { Writable } from 'svelte/store';
  import { InfoDialogExtension } from '$lib/Extensions/InfoDialog/InfoDialog.extension';
  import { SearchReplaceExtension } from '$lib/Extensions/SearchReplace/SearchReplace.extension';
  import { DialogManagerMark } from '$lib/Extensions/extension-dialog-manager/extension-dialog-manager.mark';
  import { ReadonlyMark } from '$lib/Toolbar/marks/readonlyMark';
  import { BreakNode } from '$lib/ssml/nodes/breakNode';
  import { EmphasisMark } from '$lib/ssml/marks/emphasisMark';
  import { PhonemeMark } from '$lib/ssml/marks/phonemeMark';
  import { ProsodyMark } from '$lib/ssml/marks/prosodyMark';
  import { SayasMark } from '$lib/ssml/marks/sayasMark';
  import { SilentMark } from '$lib/ssml/marks/silentMark';
  import { SubMark } from '$lib/ssml/marks/subMark';
  import { sanitizeHtmlForSsml } from './ssml/ssml';
  import { AutomarkupMark } from './Extensions/automarkupMark';
  import { CommentMark } from './Extensions/commentMark';
  import { ChatExtension } from '$lib/Extensions/Chat/Chat.extension';
  import { ChatMark } from '$lib/Extensions/Chat/Chat.mark';
  import type { ChatCurrentUser } from '$lib/Extensions/Chat/Chat.types';
  import { CharacterCount } from './Extensions/extension-character-count/character-count';
  import { Voice } from './Extensions/extension-voice/extension-voice';

  type RangeSelection = { from: number; to: number };

  const textEditorContext = writable<TextEditorContext>({
    isFullScreenOpen: false,
    isDictionariesChosen: false,
    spellingDictionaries: [],
    disallowedDictionaries: [],
    isProofreadingRunning: false,
    isProofreadingEnabled: false,
    selectedSpellingDictionariesIds: [],
    selectedDisallowedTermsDictionariesIds: [],
  });

  setContext('textEditor', textEditorContext);

  /** The internal HTML representation of editor contents */
  export let html: JSONContent | string = '<p></p>';

  /**
   * The features/buttons to enable/show for this editor.
   */
  export let features: FeatureConfiguration = {};

  /**
   * The options used by the editor to parse html
   */
  export let parseOptions: ParseOptions = {};

  /**
   * The props used by the editor
   */
  export let editorProps: EditorOptions['editorProps'] | undefined = undefined;

  /**
   * Should the editor be made editable?
   */
  export let editable = true;

  /**
   * After creation of the editor, should the editor be focused and the text cursor be placed
   * in the text, so that the user can start editing immediately?
   */
  export let autofocus: 'start' | 'end' | 'all' | number | boolean | null = false;

  /**
   * The current cursor position
   */
  export let currentPosition = 0;

  /**
   * The maximum allowable position based on text length
   */
  export let maxPosition = 0;

  /**
   * The size of toolbar
   */
  export let toolbarSize: 'default' | 'small' = 'default';

  /**
   * The list of font families. They are needed if you are activated fontFamily extension
   */
  export let fontFamilies: DropdownItems = [];

  /**
   * Whether the editor is resizable, and if so, in which directions.
   * @type {'none' | 'both' | 'horizontal' | 'vertical' | 'block' | 'inline'}
   * @default 'none'
   */
  export let resize: 'none' | 'both' | 'horizontal' | 'vertical' | 'block' | 'inline' = 'none';

  /**
   * The height of the editor
   * @type {string}
   * @default '200px'
   */
  export let height = '200px';

  /**
   * The font zoom level
   * @type {number}
   * @default 100
   */
  export let fontZoom = 100;

  /**
   * The minimum font size for the font zoom slider
   */

  export let minFontSize = 100;

  /**
   * The maximum font size for the font zoom slider
   */

  export let maxFontSize = 400;

  /**
   * Callback for when the font zoom level changes
   */

  export let eventHandlers: EventHandlers = {};

  /**
   * The language of the text editor
   */
  export let languageCode: string | undefined = undefined;

  /**
   * The list of external extensions to add to the editor
   */

  export let showStatusBarOnFocus = false;

  /**
   * Whether to also show custom toolbar content in the bubble menu
   */
  export let showToolbarRightsideSlotInBubbleMenu = false;

  /**
   * Light version
   */
  export let light = false;

  /**
   * The current user
   */

  export let currentUser: ChatCurrentUser | undefined = undefined;

  /**
   * Get the editor instance
   */

  export function getEditor() {
    return editor;
  }

  /**
   * Cut the text after a given position
   * @param position
   */

  export function cutTextAfter(position: number) {
    // ProseMirror positions start at 1
    const index = position + 1;
    const maxIndex = maxPosition + 1;

    if (index <= 0 || index > maxIndex || !editor) {
      return;
    }

    const before: JSONContent = editor.state.doc.cut(1, index).toJSON();
    const after: JSONContent = editor.state.doc.cut(index).toJSON();

    const newHtml = generateHTML(before, editor.options.extensions);
    editor.commands.setContent(newHtml);
    return generateHTML(after, editor.options.extensions);
  }

  /**
   * Gets the configured shortcuts (configured or default as fallback) for a given feature.
   * @param {Feature} feature feature
   * @return {string[]} shortcuts
   */

  export function getShortcuts(feature: Feature): string[] {
    return features[feature]?.shortcuts ?? getDefaultShortcuts(feature);
  }

  /**
   * Set the text editor content
   * @param {string} newHtml the new content
   */

  export let externalExtensions: AnyExtension[] = [];

  /**
   * Allow Text Editor to be Max Height of parent container
   */

  export let verticalDock = false;

  let characterCount = 0;

  $: if (editor && editable !== editor.isEditable) {
    editor.setEditable(editable);
    editor = editor;
  }
  $: updateContents(html);

  function updateContents(newHtml: string | JSONContent) {
    if (editor && newHtml !== editor.getHTML()) {
      editor.commands.setContent(newHtml, false, parseOptions);
    }
  }

  const dispatch = createEventDispatcher<{
    change: string;
    blur: string;
    blurjson: JSONContent;
    selectionUpdate: RangeSelection;
    updated: Editor;
    focus: string;
  }>();

  const eventErrors: string[] = [];
  const wrappedEventHandlers = withErrorHandling(eventHandlers);
  const {
    getAllLanguages,
    onProofread,
    getDisallowedDictionaries,
    getSpellingDictionaries,
    onLinkHover,
    selectedThread,
    newComment,
    editComment,
    threadResolved,
    threadDeleted,
    commentDeleted,
    newThread,
    onCharacterCountWarning,
  } = wrappedEventHandlers;
  const bubbleMenuID = crypto.randomUUID();
  const textEditorContextStore: Writable<TextEditorContext> = getContext('textEditor');

  let toolbarElement: HTMLElement | undefined;
  let statusBarElement: HTMLElement | undefined;
  let editorContainer: HTMLDivElement;
  let editor: Editor;
  let editorParentContainer: HTMLElement | undefined;
  let originalParent: ParentNode | undefined | null = undefined;
  let portalTarget: PortalTarget | undefined = undefined;
  let selectedLanguage: Language | undefined = undefined;
  let isErrorModalEnabled = false;
  let hideTimer: ReturnType<typeof setTimeout> | undefined;
  let hideStatusBar = true;
  let isElementClicked = false;
  let resizeObserver: ResizeObserver;

  $: void getDictionaries(languageCode, getSpellingDictionaries, getDisallowedDictionaries);

  export function getHtmlWithSsmlProcessed() {
    return sanitizeHtmlForSsml(editor);
  }

  async function getDictionaries(
    language?: string,
    spellingDictionariesFn?: DictionaryCallback,
    disallowedDictionariesFn?: DictionaryCallback
  ) {
    if (!language || !spellingDictionariesFn || !disallowedDictionariesFn) {
      textEditorContextStore.update((context) => ({
        ...context,
        isProofreadingEnabled: false,
      }));
      return;
    }

    const [spelling, disallowed] = await Promise.all([
      spellingDictionariesFn(language),
      disallowedDictionariesFn(language),
    ]);

    textEditorContextStore.update((context) => ({
      ...context,
      spellingDictionaries: spelling,
      disallowedDictionaries: disallowed,
      isProofreadingEnabled: true,
    }));
  }

  /**
   * Update the writing-mode for the whole editor, according to the first paragraph's writing mode.
   * @param {Transaction} transaction an editor transaction
   */
  function updateWritingMode(transaction: Transaction | undefined) {
    // should be Transaction instead of any
    editorContainer.style.writingMode = transaction?.doc.firstChild?.attrs.writingMode;
  }

  /**
   * Return true if a given feature is active
   * @param {Feature} feature feature to check
   * @return {boolean} true iff the selected feature should be enabled
   */
  function isFeatureActive(feature: Feature): boolean {
    return Object.keys(features).includes(feature) && typeof features[feature] === 'object';
  }

  /**
   * Return the options object from a requested feature
   * @param {Feature} feature feature to check
   * @return {Record<string, any>} options object
   */
  function getFeatureOptions(feature: Feature): Record<string, any> | undefined {
    return isFeatureActive(feature) ? features[feature]?.options : {};
  }

  /**
   * Override existing shortcuts.
   * @remarks
   * Using {@link getExtensionField}, retrieve the current keyboard shortcuts
   * of the given extension. This information is an object with the shortcuts as
   * keys and the commands to invoke as value.
   * Modify the keys, according to the configured shortcut list.
   * In TipTap extensions can be extended. To get the actual value of the field
   * of an extension, that extension has to queried and potentially all of it's
   * parent extensions. The TipTap-way to do this is `getExtensionField`. See
   * {@link https://github.com/ueberdosis/tiptap/blob/28737b1631719315cebc354f7cb5a1d924b0b543/packages/core/src/helpers/getExtensionField.ts}
   * for the source of that function. Examples of how to use it can be found here:
   * {@link https://github.com/ueberdosis/tiptap/blob/28737b1631719315cebc354f7cb5a1d924b0b543/packages/core/src/ExtensionManager.ts}.
   * Looking at {@link https://github.com/ueberdosis/tiptap/blob/main/packages/extension-text-align/src/text-align.ts},
   * this would mean to overwrite the four keyboard shortcuts, while keeping the
   * commands with their parameters.
   * @param newShortcuts new shortcuts to use
   * @param configuredExtension extension for which to override shortcuts
   * @returns new extension with overridden shortcuts
   */

  function configureShortcuts(newShortcuts: string[], configuredExtension: AnyExtension) {
    return configuredExtension.extend({
      addKeyboardShortcuts() {
        // retrieve old shortcuts
        const addKeyboardShortcuts = getExtensionField(
          configuredExtension,
          'addKeyboardShortcuts',
          this
        );
        const shortcuts: Record<string, KeyboardShortcutCommand> =
          addKeyboardShortcuts?.call(this) || {};

        // overwrite keys
        return Object.fromEntries(
          newShortcuts.map((_, i): [string, KeyboardShortcutCommand] => [
            newShortcuts[i],
            Object.values<KeyboardShortcutCommand>(shortcuts)[i],
          ])
        );
      },
    });
  }

  /**
   * Adds all necessary TipTap extensions, according to feature configuration.
   * @return {AnyExtension[]} list of extensions
   */
  function extensionsFromFeatures(): AnyExtension[] {
    const extensions: AnyExtension[] = [Document, Paragraph, Text];
    if (!isFeatureActive(Feature.Collaboration)) {
      extensions.push(History);
    }

    const alignments: string[] = [];

    Object.values(Feature)
      .filter((feature) => isFeatureActive(feature))
      .forEach((feature) => {
        const shortcuts = getShortcuts(feature);

        switch (feature) {
          case Feature.CharacterCount:
            extensions.push(
              CharacterCount.configure({
                limit: getFeatureOptions(Feature.CharacterCount)?.charactersLimit,
                warning: onCharacterCountWarning,
              })
            );
            break;
          case Feature.FontSize:
            extensions.push(FontSize);
            break;
          case Feature.FontFamily:
            extensions.push(FontFamily);
            break;
          case Feature.Bold:
            extensions.push(configureShortcuts(shortcuts, Bold));
            break;
          case Feature.Italicize:
            extensions.push(configureShortcuts(shortcuts, Italic));
            break;
          case Feature.AlignLeft:
            alignments.push('left');
            break;
          case Feature.AlignCenter:
            alignments.push('center');
            break;
          case Feature.AlignRight:
            alignments.push('right');
            break;
          case Feature.AlignJustify:
            alignments.push('justify');
            break;
          case Feature.TextColor:
            extensions.push(TextStyle);
            extensions.push(configureShortcuts(shortcuts, TextColor));
            break;
          case Feature.TextColorMenu:
            extensions.push(Color.configure({ types: ['textStyle', 'heading', 'paragraph'] }));
            break;
          case Feature.Underline:
            extensions.push(configureShortcuts(shortcuts, Underline));
            break;
          case Feature.Superscript:
            extensions.push(configureShortcuts(shortcuts, Superscript));
            break;
          case Feature.Subscript:
            extensions.push(configureShortcuts(shortcuts, Subscript));
            break;
          case Feature.TextDirection:
            extensions.push(configureShortcuts(shortcuts, TextDirection));
            break;
          case Feature.BulletList:
            if (!extensions.includes(ListItem)) {
              extensions.push(ListItem);
            }
            extensions.push(configureShortcuts(shortcuts, BulletList));
            break;
          case Feature.OrderedList:
            if (!extensions.includes(ListItem)) {
              extensions.push(ListItem);
            }
            extensions.push(configureShortcuts(shortcuts, OrderedList));
            break;
          case Feature.Strikethrough:
            extensions.push(configureShortcuts(shortcuts, Strike));
            break;
          case Feature.InsertLink:
            extensions.push(
              configureShortcuts(
                shortcuts,
                Link.extend({
                  addProseMirrorPlugins() {
                    return [
                      clickHandler({ type: this.type }),
                      autolink({
                        type: this.type,
                        validate: this.options.validate,
                      }),
                    ];
                  },
                })
              )
            );
            break;
          case Feature.Highlight:
            extensions.push(
              configureShortcuts(shortcuts, Highlight).configure({ multicolor: true })
            );
            break;
          case Feature.SearchReplace:
            extensions.push(
              SearchReplaceExtension.configure({
                searchOnly: getFeatureOptions(Feature.SearchReplace)?.searchOnly,
              })
            );
            break;
          case Feature.Ssml:
            extensions.push(
              BreakNode,
              EmphasisMark,
              PhonemeMark,
              ProsodyMark,
              SayasMark,
              SilentMark,
              SubMark
            );
            extensions.push(
              Voice.configure({
                getIPACode: getFeatureOptions(Feature.Voice)?.getIPACode,
                getBase64AudioFromText: getFeatureOptions(Feature.Voice)?.getBase64AudioFromText,
              })
            );
            break;
          case Feature.Editable:
            break; // no extension needed
          case Feature.TrackChanges:
            extensions.push(
              TrackChanges.configure({
                enabled: features.TrackChanges?.enabled,
                dataUserId: features.TrackChanges?.dataUserId,
                dataUserName: features.TrackChanges?.dataUserName,
              })
            );
            break;
          case Feature.Chat:
            extensions.push(
              ChatExtension.configure({
                currentUser: currentUser,
                commentEvents: {
                  selectedThread,
                  newComment,
                  editComment,
                  threadResolved,
                  threadDeleted,
                  newThread,
                  commentDeleted,
                },
              }),
              ChatMark.configure()
            );
            break;
          case Feature.DialogManager:
            extensions.push(
              DialogManagerExtension.configure({
                onMarkAdded: getFeatureOptions(Feature.DialogManager)?.onMarkAdded,
                overflowMenuItems: getFeatureOptions(Feature.DialogManager)?.overflowMenuItems,
                title: getFeatureOptions(Feature.DialogManager)?.title,
                borderColor: getFeatureOptions(Feature.DialogManager)?.borderColor,
                backgroundColor: getFeatureOptions(Feature.DialogManager)?.backgroundColor,
                initialMenuItems: getFeatureOptions(Feature.DialogManager)?.initialMenuItems,
                slotComponent: getFeatureOptions(Feature.DialogManager)?.slotComponent,
              }),
              DialogManagerMark.configure({
                markBackgroundColor: getFeatureOptions(Feature.DialogManager)?.backgroundColor,
              })
            );
            break;
          case Feature.Table:
            extensions.push(
              TableFeature.configure({
                HTMLAttributes: {
                  class: 'text-editor-table',
                },
              }),
              TableRow.configure({
                HTMLAttributes: {
                  class: 'text-editor-table-row',
                },
              }),
              TableCell.configure({
                HTMLAttributes: {
                  class: 'text-editor-table-cell',
                },
              }),
              TableHeader.configure({
                HTMLAttributes: {
                  class: 'text-editor-table-header',
                },
              })
            );
            break;
          default:
            break;
        }
      });

    if (alignments.length > 0) {
      const extension = configureShortcuts(
        [
          ...getShortcuts(Feature.AlignLeft),
          ...getShortcuts(Feature.AlignCenter),
          ...getShortcuts(Feature.AlignRight),
          ...getShortcuts(Feature.AlignJustify),
        ],
        TextAlign
      );

      extensions.push(
        extension.configure({
          types: ['paragraph'],
          alignments: alignments,
          defaultAlignment: alignments[0],
        })
      );
    }

    if (isBubbleMenuEnabled()) {
      extensions.push(
        BubbleMenuExtension.configure({
          element: document.getElementById(bubbleMenuID),
          tippyOptions: {
            onClickOutside: (instance) => {
              instance.hide();
            },
          },
        })
      );
    }

    if (isInfoDialogEnabled()) {
      extensions.push(
        InfoDialogExtension.configure({
          onLinkHover: onLinkHover,
        })
      );
    }

    if ($textEditorContextStore.isProofreadingEnabled) {
      extensions.push(
        ProofreaderExtension.configure({
          proofreaderCallback: onProofread,
        })
      );
    }

    extensions.push(
      Heading.configure({
        levels: [1, 2, 3],
      }),
      ReadonlyMark.configure(),
      AutomarkupMark.configure(),
      CommentMark.configure()
    );
    return extensions;
  }

  /**
   * Check if any statusbar feature is enabled
   * @return {boolean} true if any statusbar feature is enabled
   */
  function isStatusbarEnabled(): boolean {
    return Object.values(features).some((feature) => feature.statusbar);
  }

  /**
   * Check if bubble menu is enabled
   * @return {boolean} true if bubble menu is enabled
   */
  function isBubbleMenuEnabled(): boolean {
    return (
      ($$slots['toolbar-rightside'] && showToolbarRightsideSlotInBubbleMenu) ||
      isBubbleMenuFeatureEnabled()
    );
  }

  /**
   * Check if bubble menu feature is enabled
   * @return {boolean} true if any bubble menu feature is enabled
   */
  function isBubbleMenuFeatureEnabled(): boolean {
    return Object.values(features).some((feature) => feature.bubbleMenu);
  }

  function isInfoDialogEnabled(): boolean {
    return typeof onLinkHover === 'function';
  }

  /**
   * Toggle full screen mode
   */

  function toggleFullScreen(): void {
    textEditorContextStore.update((context) => {
      return { ...context, isFullScreenOpen: !context.isFullScreenOpen };
    });

    if ($textEditorContextStore.isFullScreenOpen) {
      if (editorParentContainer) {
        originalParent = editorParentContainer.parentNode;
        portalTarget = portal(editorParentContainer, 'body');
      }
    } else {
      if (portalTarget) {
        portalTarget.destroy();
        portalTarget = undefined;
      }
    }

    if (!$textEditorContextStore.isFullScreenOpen && originalParent && editorParentContainer) {
      originalParent.appendChild(editorParentContainer);
    }
  }

  /**
   * Handle language selection
   */

  async function handleLanguageSelection() {
    try {
      if (!getAllLanguages || !languageCode) {
        return;
      }

      const languages = await getAllLanguages();
      selectedLanguage = languages.find((lang) => lang.languageCode === languageCode);
    } catch {
      eventErrors.push('getAllLanguages');
    }
  }

  /**
   * Wraps event handlers with error handling
   * @param eventHandlersFunctions
   */

  function withErrorHandling(eventHandlersFunctions: EventHandlers): EventHandlers {
    const wrappedEvents: EventHandlers = {};

    Object.keys(eventHandlersFunctions).forEach((key) => {
      const originalCallback = eventHandlersFunctions[key];

      if (typeof originalCallback === 'function') {
        try {
          originalCallback();
        } catch {
          eventErrors.push(key);
        }
      }

      wrappedEvents[key] = originalCallback;
    });

    return wrappedEvents;
  }

  /**
   * Handle the visibility of the status bar
   */
  function determineStatusbarVisibility(hasMouseExited: boolean) {
    if (editor.isFocused) {
      return;
    }

    if (!isElementClicked || hasMouseExited) {
      hideTimer = setTimeout(() => {
        hideStatusBar = true;
      }, 1000);
    }

    isElementClicked = false;
  }

  /**
   * Prevent the status bar from hiding when an element is clicked
   */
  function preventBlur() {
    isElementClicked = true;
    if (hideTimer) {
      clearTimeout(hideTimer);
      hideTimer = undefined;
      hideStatusBar = false;
    }
  }

  /**
   * Update the height of the editor
   *
   * Note: To better adapt to the parent container,we use javascript instead of css to set the height of the editor.
   * This is because the parent container might have a dynamic height, and we want the editor to adapt to that.
   * Using only CSS would require the parent container to have a fixed height, which is not always the case.
   * If we use 100% height, the editor will take the height of the parent container, but the toolbar and status bar
   * will be outside of the parent container.
   *
   * As well, when typing in the editor, the editor will grow in height, but the parent container will not grow with it.
   */

  function updateEditorHeight() {
    if (verticalDock) {
      const toolbarHeight = toolbarElement?.clientHeight ?? 0;
      const statusBarHeight = statusBarElement?.clientHeight ?? 0;
      const parentHeight =
        (editorParentContainer?.parentElement?.clientHeight ?? 0) -
        (toolbarHeight + statusBarHeight);
      editorContainer.style.height = `${parentHeight.toString()}px`;
    }
  }

  onMount(async () => {
    editor = new Editor({
      element: editorContainer,
      content: html,
      editable: editable,
      autofocus: autofocus,
      parseOptions: parseOptions,
      editorProps: editorProps ?? {
        // this is a workaround for https://github.com/ueberdosis/tiptap/issues/1530
        // Note: returning false here will allow the paste event to propagate and be handled by tiptap
        handlePaste: (_, event: ClipboardEvent) => {
          const pastedHtml = event.clipboardData?.getData('text/html');
          if (pastedHtml) {
            // only do this if we pasting multiple paragraphs because each might have different alignments
            const json = generateJSON(pastedHtml, editor.options.extensions);
            if (json.content.length > 1) {
              editor.commands.insertContent(json);
              return true;
            }
          }
          return false;
        },
        attributes: {
          class: 'editorClass',
        },
      },
      extensions: [
        ...extensionsFromFeatures(),
        ...externalExtensions,
        ProofreaderExtension.configure({
          proofreaderCallback: onProofread,
        }),
      ],
      onTransaction: (event) => {
        updateWritingMode(event.transaction);
        editable = event.editor.isEditable;
        currentPosition = event.editor.view.state.selection.anchor - 1;
        maxPosition = event.editor.state.doc.slice(1).size - 1;
      },
      onUpdate: (event) => {
        /** @event {{ html: string; }} change */
        dispatch('change', event.editor.getHTML());
        dispatch('updated', event.editor);
        characterCount =
          isFeatureActive(Feature.CharacterCount) && editor.storage.characterCount
            ? editor.storage.characterCount.characters()
            : 0;
        console.log('get text editor json', event.editor.getJSON());
      },
      onFocus: (event) => {
        /** @event {{ html: string; }} focus */
        hideStatusBar = false;
        dispatch('focus', event.editor.getHTML());
      },
      onBlur: (event) => {
        /** @event {{ html: string; }} blur */
        dispatch('blur', event.editor.getHTML());
      },
      onSelectionUpdate: (event) => {
        const innerEditor = event.editor;
        const { from, to } = innerEditor.state.selection;
        if (from !== to) {
          /** @event {{ from: number; to: number; }} selectionUpdate */
          dispatch('selectionUpdate', { from, to });
        }
      },
    });

    if (editorParentContainer?.parentElement) {
      resizeObserver = new ResizeObserver(() => {
        updateEditorHeight();
      });
      resizeObserver.observe(editorParentContainer.parentElement);
    }

    updateEditorHeight();

    if (getAllLanguages && languageCode) {
      await handleLanguageSelection();
    }

    characterCount =
      isFeatureActive(Feature.CharacterCount) && editor.storage.characterCount
        ? editor.storage.characterCount.characters()
        : 0;
  });

  onDestroy(() => {
    if (editor) {
      editor.destroy();
    }
    if (verticalDock) {
      resizeObserver.disconnect();
    }
  });
</script>

<div
  on:mouseleave={() => determineStatusbarVisibility(true)}
  class:text-editor--full-screen={$textEditorContextStore.isFullScreenOpen}
  class:text-editor--light={light}
  bind:this={editorParentContainer}
  role="toolbar"
  tabindex="0">
  <Toolbar
    bind:toolbarElement
    {editor}
    featureConfiguration={features}
    size={toolbarSize}
    shortcutGetter={getShortcuts}
    {fontFamilies}>
    <slot name="toolbar-rightside" />
  </Toolbar>
  <div
    bind:this={editorContainer}
    class:text-editor-container--max-height={verticalDock}
    class="text-editor-container editor-container"
    spellcheck="false"
    style="--resize: {resize}; --height: {verticalDock
      ? 'auto'
      : $textEditorContextStore.isFullScreenOpen
      ? '100%'
      : height}; --font-size: {fontZoom}%;" />
  {#if isStatusbarEnabled()}
    <Statusbar
      bind:statusBarElement
      bind:editor
      on:openFullScreen={toggleFullScreen}
      on:openErrorDialog={() => (isErrorModalEnabled = true)}
      on:clickedElement={preventBlur}
      hideStatusBar={showStatusBarOnFocus && hideStatusBar}
      eventHandlers={wrappedEventHandlers}
      {selectedLanguage}
      {eventErrors}
      {maxFontSize}
      {minFontSize}
      {features}
      showCharacterCounter={getFeatureOptions(Feature.CharacterCount)?.showCharacterCounter}
      {characterCount}
      charactersLimit={getFeatureOptions(Feature.CharacterCount)?.charactersLimit}
      bind:fontZoom />
  {/if}
</div>

{#if eventErrors.length > 0}
  <Portal target="body">
    <Modal
      on:close={() => (isErrorModalEnabled = false)}
      modalHeading=""
      primaryButtonText="OK"
      danger={true}
      open={isErrorModalEnabled}
      on:click:button--primary={() => (isErrorModalEnabled = false)}
      size="xs">
      <div class="inner-wrapper-error-modal">
        <h5>The following events have returned errors:</h5>
        <ul>
          {#each eventErrors as error}
            <li>{error}</li>
          {/each}
        </ul>
      </div>
    </Modal>
  </Portal>
{/if}

{#if isBubbleMenuEnabled()}
  <BubbleMenu
    {bubbleMenuID}
    {editor}
    featureConfiguration={features}
    shortcutGetter={getShortcuts}
    {fontFamilies}>
    <slot name="toolbar-rightside" />
  </BubbleMenu>
{/if}

<style scoped>
  :global([data-tippy-root] .tippy-box) {
    max-width: 468px !important;
  }

  .text-editor-container--max-height {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }

  .text-editor-container--max-height :global(.editorClass) {
    flex: 1;
    height: 100%;
    max-height: 100%;
    resize: none;
    overflow: auto;
    box-sizing: border-box;
  }

  .text-editor--light {
    --editor-bg: var(--cds-ui-background, #ffffff);
  }
  .text-editor--full-screen {
    padding: 1rem;
    display: flex;
    flex-direction: column;
  }
  .text-editor--full-screen .text-editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .text-editor--full-screen .text-editor-container :global(.editorClass) {
    height: calc(100vh - 148px);
  }
  .text-editor-container {
    position: relative;
  }
  .text-editor-container :global(.editorClass) {
    padding: 10px 10px 10px 10px;
    resize: var(--resize);
    height: var(--height);
    font-size: var(--font-size);
    border: 1px solid black;
    overflow: auto;
    background-color: var(--editor-bg, var(--cds-field-01, #ffffff));
    z-index: 5;
  }
  .text-editor-container :global(.editorClass *:not(img)) {
    font-size: inherit;
  }
  .text-editor-container :global(.editorClass ul) {
    list-style-type: disc;
    margin-left: 2rem;
  }
  .text-editor-container :global(.editorClass ol) {
    list-style-type: decimal;
    margin-left: 2rem;
  }

  :global(.error-warning:focus) {
    border-color: transparent !important;
    outline: unset !important;
    box-shadow: unset !important;
  }
  :global(.error-warning span),
  :global(.error-warning::before) {
    display: none !important;
  }
  .inner-wrapper-error-modal h5 {
    margin-bottom: 1em;
  }
  .inner-wrapper-error-modal ul {
    list-style-type: disc;
    margin-left: 1.5em;
  }
  .inner-wrapper-error-modal ul li {
    margin-bottom: 0.5em;
  }
  :global(.bx--modal) {
    z-index: 99991;
  }
  :global([data-tippy-root] .tippy-box) {
    max-width: none !important;
  }
  :global(.tippy-content .bx--btn) {
    padding: 5px 5px 5px 5px;
    line-height: 1;
    min-width: 35px;
    min-height: 35px;
    text-align: center;
    display: block;
    justify-content: center;
    align-items: center;
  }
  .text-editor-container :global(.editorClass > h1) {
    font-size: 200%;
    font-weight: 600;
    margin-top: 10px;
    margin-bottom: 5px;
  }
  .text-editor-container :global(.editorClass > h2) {
    font-size: 160%;
    font-weight: 600;
    margin-top: 10px;
    margin-bottom: 5px;
  }
  .text-editor-container :global(.editorClass > h3) {
    font-size: 120%;
    font-weight: 600;
    margin-top: 10px;
    margin-bottom: 5px;
  }
  :global(.tiptap .text-editor-read-only) {
    opacity: 0.7;
  }
  :global(.tiptap .text-editor-automarkup) {
    color: blue;
  }
  :global(.tiptap .text-editor-comment) {
    color: green;
  }

  :global(.tiptap .text-editor-table) {
    border-collapse: collapse;
    margin: 0;
    overflow: hidden;
    table-layout: fixed;
  }

  :global(.tiptap .text-editor-table-header) {
    box-sizing: border-box;
    min-width: 1em;
    padding: 6px 8px;
    position: relative;
    vertical-align: top;
    background-color: rgb(202, 202, 202);
  }

  :global(.tiptap .text-editor-table-header p) {
    font-weight: bold;
  }

  :global(.tiptap .text-editor-table-cell) {
    border: 1px solid;
    box-sizing: border-box;
    min-width: 1em;
    padding: 6px 8px;
    position: relative;
    vertical-align: top;
    margin-bottom: 0;
  }

  :global(.tiptap .text-editor-table-row) {
    border: 1px solid;
    box-sizing: border-box;
    min-width: 1em;
    padding: 6px 8px;
    position: relative;
    vertical-align: top;
    margin-bottom: 0;
  }

  :global(.spellcheck-error) {
    text-decoration: underline red;
    -webkit-text-decoration: underline red;
    text-decoration-style: wavy;
    -webkit-text-decoration-style: wavy;
    text-decoration-thickness: 2px;
    -webkit-text-decoration-thickness: 2px;
  }

  :global(.disallowed-term-error) {
    text-decoration: underline rgba(145, 32, 77, 1);
    -webkit-text-decoration: underline rgba(145, 32, 77, 1);
    text-decoration-style: wavy;
    -webkit-text-decoration-style: wavy;
    text-decoration-thickness: 2px;
    -webkit-text-decoration-thickness: 2px;
  }
  :global(ul.match-widget) {
    background: #fff;
    padding: 0.5rem;
    width: 250px;
    border-radius: 4px;
    margin-left: 0 !important;
    list-style-type: none !important;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
  }
  :global(ul.match-widget li) {
    padding: 0.3rem 0.8rem 0.3rem 0.3rem;
  }
  :global(ul.match-widget li:hover) {
    background: #f0f0f0;
    cursor: pointer;
  }

  :global(div.match-widget) {
    z-index: 1000;
    background-color: rgba(145, 32, 77, 0.1);
    padding: 0.5rem 1rem 0.5rem 0.5rem;
    border-radius: 4px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
  }

  :global(.match-widget.hidden) {
    display: none;
  }

  :global(.comments-mark) {
    background-color: rgba(255, 230, 179, 0.3);
    border-bottom: 2px dotted #ffa500;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  :global(.content-mark) {
    background-color: #e6fdef;
  }

  :global(.content-mark:hover) {
    cursor: pointer;
  }

  :global(.comments-mark:hover) {
    cursor: pointer;
    background-color: rgba(255, 230, 179, 0.5);
  }

  @font-face {
    font-family: 'MEPS Markup';
    src: url('../fonts/Markup.woff2') format('woff2'), url('../fonts/Markup.woff') format('woff'),
      url('../fonts/Markup.ttf') format('truetype');
  }
  @font-face {
    font-family: mongol;
    src: url('../fonts/Menksoft2007.woff') format('woff');
  }

  :global(body *) {
    font-family: 'Noto Sans', 'MEPS Markup', 'mongol';
  }
</style>
