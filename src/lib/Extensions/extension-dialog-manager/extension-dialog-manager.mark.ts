import type { Command, CommandProps } from '@tiptap/core';
import { Mark, mergeAttributes } from '@tiptap/core';
import type { Transaction } from '@tiptap/pm/state';
import type { MarkID } from './extension-dialog-manager.types';
import type {
  RenderHTMLProps,
  RenderHTMLReturn,
  DialogManagerMarkOptions,
} from './extension-dialog-manager.types';

type SetContentMarkAttributesProps = {
  from: number;
  to: number;
  type: string;
  hasComponent: boolean;
};

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    dialogManagerMark: {
      createDialogManagerMark: () => ReturnType;
      setContentMarkAttributes: (props: SetContentMarkAttributesProps) => ReturnType;
    };
  }
}

export const DialogManagerMark = Mark.create<DialogManagerMarkOptions>({
  name: 'dialogManagerMark',

  addOptions() {
    return {
      markId: undefined,
      markBackgroundColor: undefined,
      HTMLAttributes: {
        'class': 'content-mark',
        'data-mark-id': '',
        'data-type': '',
        'data-has-component': false,
      },
    };
  },

  addAttributes() {
    return {
      'class': {
        default: 'content-mark',
      },
      'data-mark-id': {
        default: '',
      },
      'style': {
        default: '',
      },
      'data-type': {
        default: '',
      },
      'data-has-component': {
        default: false,
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'content-marker',
      },
    ];
  },

  addCommands() {
    return {
      createDialogManagerMark:
        (): Command =>
        ({ state, commands, dispatch }: CommandProps) => {
          const markType = state.schema.marks[this.name];
          const hasMark = state.selection.ranges.some((range) => {
            return state.doc.rangeHasMark(range.$from.pos, range.$to.pos, markType);
          });

          if (hasMark) {
            return commands.unsetMark(this.name);
          }

          const markId: MarkID = crypto.randomUUID();

          const success = commands.setMark(this.name, {
            'class': 'content-mark',
            'data-mark-id': markId,
            ...(this.options.markBackgroundColor
              ? { style: `background-color: ${this.options.markBackgroundColor}` }
              : {}),
          });

          if (!success) {
            return false;
          }

          const tr: Transaction = state.tr.setMeta('dialogManagerMarkAdded', {
            markId,
          });

          if (dispatch) {
            dispatch(tr);
          }

          return true;
        },
      setContentMarkAttributes:
        (props: { from: number; to: number; type: string; hasComponent: boolean }): Command =>
        ({ tr, dispatch, state }: CommandProps) => {
          const { from, to, type, hasComponent } = props;
          const { schema } = state;
          const markType = schema.marks[this.name];

          let markId: string | undefined;

          state.doc.nodesBetween(from, to, (node, pos) => {
            if (!node.isText) {
              return;
            }
            node.marks.forEach((mark) => {
              if (mark.type === markType) {
                markId = mark.attrs['data-mark-id'] as string;
                const newMark = markType.create({
                  ...mark.attrs,
                  'data-type': type,
                  'data-has-component': hasComponent,
                });
                tr.removeMark(pos, pos + node.nodeSize, markType);
                tr.addMark(pos, pos + node.nodeSize, newMark);
              }
            });
          });

          if (markId) {
            tr.setMeta('dialogManagerMarkAdded', { markId });
          }

          if (dispatch) {
            dispatch(tr);
          }

          return true;
        },
    };
  },

  renderHTML({ HTMLAttributes }: RenderHTMLProps): RenderHTMLReturn {
    return ['content-marker', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0];
  },
});
