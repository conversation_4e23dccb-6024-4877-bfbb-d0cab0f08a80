<script lang="ts">
  import { OverflowMenuHorizontal, Close } from 'carbon-icons-svelte';
  import { Button, OverflowMenu, OverflowMenuItem } from 'carbon-components-svelte';
  import MenuItem from './Menu/MenuItem.svelte';
  import Menu from './Menu/Menu.svelte';
  import { createEventDispatcher } from 'svelte';
  import type { SvelteComponent } from 'svelte';
  import type { InitialMenuItem } from './extension-dialog-manager.types';

  export let borderColor = '#000000';
  export let backgroundColor = '#ffffff';
  export let title = 'DP Content';
  export let overflowItems: { text: string; onClick: () => void }[] = [];
  export let initialMenuItems: InitialMenuItem[] = [];
  export let onClose: () => void;
  export let onClick: (item: InitialMenuItem) => void = () => {};
  export let selectedType: string | undefined = undefined;
  export let selectedMarkId: string | undefined = undefined;
  export let component: typeof SvelteComponent | undefined = undefined;
  export let showMenu = true;

  const dispatch = createEventDispatcher();

  $: internalComponent = component;

  function handleInitialItemClicked(item: InitialMenuItem) {
    onClick?.(item);
    dispatch('initialItemClicked', {
      type: item.type,
      markId: selectedMarkId,
    });
  }
</script>

{#if showMenu}
  <Menu>
    {#each initialMenuItems as item}
      <MenuItem
        hasDivider={item.hasDivider}
        text={item.text}
        on:click={() => handleInitialItemClicked(item)} />
    {/each}
  </Menu>
{:else if internalComponent}
  <div class="extension-dialog-manager">
    <div
      class="extension-dialog-manager-header"
      style="border-color: {borderColor}; background-color: {backgroundColor};">
      <div class="extension-dialog-manager-header--title">
        <h2>{title}</h2>
      </div>
      <div class="extension-dialog-manager-header--actions">
        {#if overflowItems.length}
          <OverflowMenu open icon={OverflowMenuHorizontal} flipped>
            {#each overflowItems as item}
              <OverflowMenuItem text={item.text} on:click={item.onClick} />
            {/each}
          </OverflowMenu>
        {:else}
          <Button
            kind="ghost"
            icon={OverflowMenuHorizontal}
            size="small"
            iconDescription="Overflow menu" />
        {/if}
        <Button size="small" kind="ghost" icon={Close} iconDescription="Close" on:click={onClose} />
      </div>
    </div>
    <div
      class="extension-dialog-manager-body"
      style="border-color: {borderColor}; background-color: white;">
      <svelte:component this={internalComponent} {selectedType} {selectedMarkId} />
    </div>
  </div>
{/if}

<style lang="scss">
  .extension-dialog-manager {
    width: 360px;
    background: #fff;
    &-header {
      padding: 12px 10px;
      border: 1px solid #2bac66;
      background-color: rgba(230, 253, 239, 1);
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        font-size: 0.75rem;
        font-weight: 600;
        font-family: Noto Sans, sans-serif;
        letter-spacing: 0;
      }

      &--actions {
        display: flex;
        flex-direction: row;

        :global(.bx--btn),
        :global(.bx--overflow-menu) {
          padding: 0;
          height: 20px;
          width: 20px;
          flex: 1;
          min-width: 20px;
          min-height: 20px;
        }

        :global(.bx--btn:hover) {
          background-color: transparent;
        }

        :global(.bx--btn svg) {
          fill: rgba(22, 22, 22, 1);
        }
      }
    }

    &-body {
      padding: 1rem;
      background-color: rgba(255, 255, 255, 1);
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      border: 1px solid #2bac66;
      border-top: none;
    }
  }
</style>
