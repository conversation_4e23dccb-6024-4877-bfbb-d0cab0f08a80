import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';
import type { EditorView } from 'prosemirror-view';
import type { Transaction } from 'prosemirror-state';
import { posToDOMRect } from '@tiptap/core';
import type { Editor } from '@tiptap/core';
import type { ComponentInstance, MenuItem } from './extension-dialog-manager.types';
import type {
  DialogManagerPluginProps,
  DialogManagerViewProps,
} from './extension-dialog-manager.types';
import tippy from 'tippy.js';
import type { Instance } from 'tippy.js';
import ExtensionDialogManagerComponent from './extension-dialog-manager.component.svelte';
import type { SvelteComponent } from 'svelte';

export class DPContentView {
  private view: EditorView;
  private editor: Editor;
  private processedMarks = new WeakSet<HTMLElement>();
  private readonly onMarkAdded?: (event: CustomEvent) => void;
  private componentInstance?: ComponentInstance;
  private element?: HTMLElement;
  private tippyInstance?: Instance;
  private readonly componentTitle: string;
  private readonly componentBorderColor: string;
  private readonly componentBackgroundColor: string;
  private readonly initialMenuItems: MenuItem[];
  private readonly slotComponent?: typeof SvelteComponent;
  private readonly overflowMenuItems: MenuItem[] | undefined;
  private currentCommentMark?: { from: number; to: number };

  constructor({
    view,
    editor,
    onMarkAdded,
    overflowMenuItems,
    title,
    borderColor,
    backgroundColor,
    slotComponent,
    initialMenuItems,
  }: DialogManagerViewProps) {
    this.view = view;
    this.editor = editor;
    this.onMarkAdded = onMarkAdded;
    this.overflowMenuItems = overflowMenuItems;
    this.componentTitle = title ?? 'DP Content';
    this.componentBorderColor = borderColor ?? '#2BAC66';
    this.componentBackgroundColor = backgroundColor ?? '#E6FDE5';
    this.initialMenuItems = initialMenuItems ?? [];
    this.slotComponent = slotComponent;

    this.setupMarkListener();
    this.createTippyInstance();
    this.createComponent();
  }

  update(view: EditorView) {
    this.view = view;
  }

  destroy() {
    this.tippyInstance?.destroy();
  }

  private handleInitialMenuItemClick = (item: MenuItem) => {
    this.editor.commands.setContentMarkAttributes({
      from: this.currentCommentMark?.from ?? 0,
      to: this.currentCommentMark?.to ?? 0,
      type: item.type,
      hasComponent: !!item.component,
    });

    if (!item.component) {
      item.onClick();
    }

    this.componentInstance?.$set({
      selectedType: item.type,
      component: item.component,
    });

    this.showComponent();

    this.componentInstance?.$set({ showMenu: false });
  };

  private createTippyInstance() {
    this.element = document.createElement('div');
    this.tippyInstance = tippy(this.editor.options.element, {
      content: this.element,
      allowHTML: true,
      interactive: true,
      trigger: 'manual',
      placement: 'bottom-start',
      appendTo: document.body,
    });
  }

  private createComponent() {
    if (!this.element) {
      return;
    }

    this.componentInstance = new ExtensionDialogManagerComponent({
      target: this.element,
      props: {
        borderColor: this.componentBorderColor,
        backgroundColor: this.componentBackgroundColor,
        title: this.componentTitle,
        onClose: this.onClose.bind(this),
        onClick: this.handleInitialMenuItemClick,
        overflowItems: this.overflowMenuItems ?? [],
        initialMenuItems: this.initialMenuItems,
        selectedType: undefined,
        selectedMarkId: undefined,
        component: undefined,
        showMenu: true,
      },
    });
  }

  private setupRichContentListeners() {
    this.getMarks().forEach((mark) => {
      const htmlMark = mark as HTMLElement;
      if (this.processedMarks.has(htmlMark)) {
        return;
      }
      htmlMark.addEventListener('click', () => this.handleMarkClick(htmlMark));
      this.processedMarks.add(htmlMark);
    });
  }

  private getAnchorPosition(anchorTag: HTMLAnchorElement) {
    const pos = this.view.posAtDOM(anchorTag, 0);
    const node = this.view.state.doc.resolve(pos).nodeAfter;
    return node ? { from: pos, to: pos + node.nodeSize } : { from: 0, to: 0 };
  }

  private handleMarkClick(mark: HTMLElement) {
    const markId = mark.getAttribute('data-mark-id') ?? undefined;
    const typeAttr = mark.getAttribute('data-type') ?? undefined;
    const hasComponent = mark.getAttribute('data-has-component') === 'true';

    if (!this.onMarkAdded || !this.tippyInstance || !this.componentInstance) {
      return;
    }

    this.onMarkAdded({ detail: { action: 'mark-clicked', markId } } as CustomEvent);

    const { from, to } = this.getAnchorPosition(mark as HTMLAnchorElement);
    this.currentCommentMark = { from, to };
    this.tippyInstance.setProps({
      getReferenceClientRect: () => posToDOMRect(this.view, from, to),
    });

    const selectedItem = this.findSelectedItem(typeAttr, mark);

    if (markId && hasComponent && selectedItem?.component) {
      this.openComponent(selectedItem, markId);
      return;
    }

    if (!selectedItem) {

      if (this.initialMenuItems.length === 1) {
        const [only] = this.initialMenuItems;
        if (only.component) {
          this.openComponent(only, markId);
        } else {
          only.onClick();
        }
        return;
      }

      this.componentInstance.$set({
        selectedType: undefined,
        selectedMarkId: markId,
        component: undefined,
        showMenu: true,
      });

      this.showComponent();

      return;
    }

    if (selectedItem.component) {
      this.openComponent(selectedItem, markId);
      return;
    }

    selectedItem.onClick();
    this.componentInstance.$set({ showMenu: false });
  }

  private findSelectedItem(typeAttr: string | undefined, mark: HTMLElement): MenuItem | undefined {
    if (typeAttr) {
      return this.initialMenuItems.find((i) => i.type === typeAttr);
    }
    const textAttr = mark.getAttribute('data-text') ?? undefined;
    return textAttr ? this.initialMenuItems.find((i) => i.text === textAttr) : undefined;
  }

  private showComponent() {
    this.tippyInstance?.show();
  }

  private openComponent(item: MenuItem, markId?: string) {
    if (!this.componentInstance) {
      return;
    }

    this.componentInstance.$set({
      selectedType: item.type,
      selectedMarkId: markId,
      component: item.component,
      showMenu: false,
    });

    this.showComponent();
  }

  private onClose() {
    this.tippyInstance?.hide();
    this.view.focus();
  }

  private setupMarkListener() {
    if (this.getMarks().length) {
      this.setupRichContentListeners();
    }

    this.editor.on('update', ({ transaction }: { transaction: Transaction }) => {
      if (!transaction.docChanged || !this.onMarkAdded) {
        return;
      }
      const meta = transaction.getMeta('dialogManagerMarkAdded') as { markId: string } | null;
      if (!meta?.markId) {
        return;
      }
      this.onMarkAdded({ detail: { action: 'mark-added', markId: meta.markId } } as CustomEvent);
      this.setupRichContentListeners();
    });
  }

  private getMarks() {
    return this.view.dom.querySelectorAll('.content-mark');
  }
}

export function DialogManagerPlugin(options: DialogManagerPluginProps) {
  return new Plugin({
    key:
      typeof options.pluginKey === 'string' ? new PluginKey(options.pluginKey) : options.pluginKey,
    view: (view: EditorView) =>
      new DPContentView({
        view,
        ...options,
      }),
  });
}
