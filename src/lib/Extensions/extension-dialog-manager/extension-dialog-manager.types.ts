import type { EditorView } from 'prosemirror-view';
import type { Editor } from '@tiptap/core';
import type { PluginKey } from 'prosemirror-state';
import type ExtensionDialogManagerComponent from './extension-dialog-manager.component.svelte';
import type { SvelteComponent } from 'svelte';

export type MenuItem = {
  text: string;
  onClick: () => void;
  type: string;
  component?: typeof SvelteComponent;
};

export type InitialMenuItem = {
  text: string;
  hasDivider?: boolean;
  onClick: () => void;
  type: string;
  component?: typeof SvelteComponent;
};

export type DialogManagerViewProps = {
  view: EditorView;
  editor: Editor;
  onMarkAdded?: (event: CustomEvent) => void;
  overflowMenuItems?: MenuItem[];
  title?: string;
  borderColor?: string;
  backgroundColor?: string;
  initialMenuItems?: MenuItem[];
  slotComponent?: typeof SvelteComponent;
};

export type DialogManagerPluginProps = {
  pluginKey: PluginKey | string;
  editor: Editor;
  onMarkAdded?: (event: CustomEvent) => void;
  overflowMenuItems?: MenuItem[];
  title?: string;
  borderColor?: string;
  backgroundColor?: string;
  initialMenuItems?: MenuItem[];
  slotComponent?: typeof SvelteComponent;
};

export type DialogManagerOptions = {
  pluginKey: string;
  onMarkAdded?: (event: CustomEvent) => void;
  overflowMenuItems?: MenuItem[];
  title: string | undefined;
  borderColor: string | undefined;
  backgroundColor: string | undefined;
  initialMenuItems?: MenuItem[];
  slotComponent?: typeof SvelteComponent;
};

export type DialogManagerStorage = {
  DialogManager: undefined;
};

export type MarkID = `${string}-${string}-${string}-${string}-${string}`;

export type RenderHTMLProps = {
  HTMLAttributes: Record<string, string | number | boolean | undefined>;
};

export type RenderHTMLReturn = [string, Record<string, any>, number];

export type DialogManagerMarkOptions = {
  markId: MarkID | undefined;
  HTMLAttributes: Record<string, string | number | boolean | undefined>;
  markBackgroundColor?: string;
};

export type ComponentInstance = ExtensionDialogManagerComponent;
