<script lang="ts">
  import type { Editor } from '@tiptap/core';
  import { onMount } from 'svelte';
  import {
    Loading,
    Modal,
    Select,
    SelectItem,
    /* TextInput, */ TooltipIcon,
  } from 'carbon-components-svelte';
  import { Play, Warning } from 'carbon-icons-svelte';
  import { audioPlayerSourceHeader, getHtmlSayAs } from '../helpers/toHtml';
  import { getBase64AudioFromText, getBase64AudioFromTextExists } from '../helpers/voice';

  import AudioPlayer from '$lib/players/audioPlayer.svelte';

  // about format: https://www.w3.org/TR/2005/NOTE-ssml-sayas-20050526/#S3.1

  export let languageCode: string;
  export let text = '';
  export let interpretAs = 'digits';
  export let openSayasModal = false;
  export let editor: Editor | undefined;

  let audioText = '';
  let voiceExists = false;

  let interpretAsUsedToCalculateAudio: string | undefined = '';
  const loadingIPACode = false;
  let loadingAudioText = false;

  onMount(() => {
    voiceExists = getBase64AudioFromTextExists(editor);
  });

  /**
   * Fetches the base64 audio for the current IPA code and text.
   * Updates the audioText and phUsedToCalculateAudio variables.
   */
  async function getBase64Audio() {
    if (interpretAs && interpretAs !== interpretAsUsedToCalculateAudio) {
      audioText = '';
      loadingAudioText = true;
      const base64Audio = await getBase64AudioFromText(
        languageCode,
        getHtmlSayAs(interpretAs, text),
        editor
      );

      audioText = `${audioPlayerSourceHeader},${base64Audio}`;
      interpretAsUsedToCalculateAudio = interpretAs;
      loadingAudioText = false;
    }
  }

  async function setMark() {
    if (editor) {
      editor.chain().focus().setSayas({ interpretAs }).run();
    }
  }
</script>

<Modal
  class="ssml-modal"
  open={openSayasModal}
  size="sm"
  modalHeading="SayAs"
  primaryButtonText="Confirm"
  secondaryButtonText="Cancel"
  primaryButtonDisabled={!interpretAs}
  on:click:button--secondary={() => (openSayasModal = false)}
  on:click:button--primary={() => {
    setMark();
    openSayasModal = false;
  }}
  on:open
  on:close={() => (openSayasModal = false)}
  on:submit>
  <div id="heading-slot" slot="heading">
    Say-as
    <TooltipIcon
      direction="top"
      tooltipText="Customize how text is spoken by specifying its interpretation using the say-as tag. This lets you define formats like dates, digits, spell-outs, or other pronunciations for more precise and natural-souding speech"
      icon={Warning} />
  </div>
  <div class="modal--container" slot="default">
    <div class="modal--container__row">
      <div class="title--cell">Text</div>
      <div class="italic--cell">{text}</div>
    </div>

    <div class="modal--container__row">
      <div class="title--cell">Rendering</div>
      <Select bind:selected={interpretAs} id="ssml-modal-selector">
        <option value="" disabled selected>Select rendering</option>
        <SelectItem value="spell-out" text="Spell-out" />
        <SelectItem value="cardinal" text="Cardinal" />
        <SelectItem value="ordinal" text="Ordinal" />
        <SelectItem value="digits" text="Digits" />
      </Select>
    </div>
    {#if voiceExists}
      <div class="modal--container__row">
        <div class="title--cell">Preview</div>
        <div class="action--cell">
          {#if loadingAudioText}
            <div class="audio-player">
              <Loading small withOverlay={false} />
            </div>
          {:else if audioText !== '' && interpretAs && interpretAs === interpretAsUsedToCalculateAudio}
            <div class="audio-player">
              <AudioPlayer src={audioText} disabled={!interpretAs || audioText === ''} />
            </div>
          {/if}
        </div>

        {#if (audioText === '' && !loadingAudioText && !loadingIPACode) || (interpretAs && interpretAs !== interpretAsUsedToCalculateAudio)}
          <div class="button--cell">
            <button
              class="preview--button"
              disabled={loadingAudioText ||
                !interpretAs ||
                interpretAs === '' ||
                interpretAs === interpretAsUsedToCalculateAudio}
              on:click={async () => {
                await getBase64Audio();
              }}>
              <Play />
            </button>
          </div>
        {/if}
      </div>
    {/if}
  </div>
</Modal>

<style lang="scss">
  .action--cell {
    display: flex;
    flex-direction: row;
    min-width: 20%;
    align-self: center;
    width: 100%;
  }
  .audio-player {
    width: 75%;
  }
  .button--cell {
    min-width: 20%;
    align-self: center;
  }
  .italic--cell {
    font-style: italic;
  }
  .modal--container {
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
    background-color: #f4f4f4;
    margin-bottom: 0.5rem;
  }
  .modal--container__row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    column-gap: 2rem;
  }
  .preview--button {
    align-self: center;
    border: none;
    background-color: transparent;
    cursor: pointer;
  }
  .title--cell {
    min-width: 20%;
    align-self: center;
  }
</style>
