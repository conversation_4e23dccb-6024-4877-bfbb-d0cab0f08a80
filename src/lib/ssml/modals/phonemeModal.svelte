<script lang="ts">
  import AudioPlayer from '$lib/players/audioPlayer.svelte';
  import type { Editor } from '@tiptap/core';
  import { Button, Loading, Modal, TextInput, TooltipIcon } from 'carbon-components-svelte';
  import { Play, Warning } from 'carbon-icons-svelte';
  import { onMount } from 'svelte';
  import { audioPlayerSourceHeader, getHtmlPhoneme } from '../helpers/toHtml';
  import {
    getBase64AudioFromText,
    getBase64AudioFromTextExists,
    getIPACode,
    getIPACodeExists,
  } from '../helpers/voice';

  export let editor: Editor | undefined;
  export let languageCode: string;
  export let openPhonemeModal = false;
  export let ph: string | undefined = undefined;
  export let text: string;

  let audioText = '';
  let voiceExists = false;
  let ipaExists = false;

  let phUsedToCalculateAudio: string | undefined = '';
  let loadingIPACode = false;
  let loadingAudioText = false;

  onMount(() => {
    voiceExists = getBase64AudioFromTextExists(editor);
    ipaExists = getIPACodeExists(editor);
  });

  /**
   * Fetches the base64 audio for the current IPA code and text.
   * Updates the audioText and phUsedToCalculateAudio variables.
   */
  async function getBase64Audio() {
    if (ph && ph !== phUsedToCalculateAudio) {
      audioText = '';
      loadingAudioText = true;
      const base64Audio = await getBase64AudioFromText(
        languageCode,
        getHtmlPhoneme(ph, text),
        editor
      );

      audioText = `${audioPlayerSourceHeader},${base64Audio}`;
      phUsedToCalculateAudio = ph;
      loadingAudioText = false;
    }
  }

  /**
   * Fetches the IPA code for the current text using the getIPACode helper.
   * Updates the ph variable and resets audioText if the IPA code changes.
   */
  async function getIpaCode() {
    loadingIPACode = true;
    ph = await getIPACode(text, editor);
    loadingIPACode = false;
    if (ph !== phUsedToCalculateAudio) {
      audioText = '';
    }
  }

  /**
   * Sets the phoneme mark in the editor with the current IPA code.
   */
  async function setMark() {
    if (editor) {
      editor
        .chain()
        .focus()
        .setPhoneme({ ph: ph ?? '' })
        .run();
    }
  }
</script>

<Modal
  class="ssml-modal"
  open={openPhonemeModal}
  size="sm"
  primaryButtonText="Confirm"
  primaryButtonDisabled={!ph}
  secondaryButtonText="Cancel"
  on:click:button--secondary={() => (openPhonemeModal = false)}
  on:click:button--primary={() => {
    setMark();
    openPhonemeModal = false;
  }}
  on:open
  on:close={() => (openPhonemeModal = false)}
  on:submit>
  <div id="heading--slot" slot="heading">
    Phoneme
    <TooltipIcon
      direction="top"
      tooltipText="Customize pronunciation by specifying the phonetic spelling of words of sounds. Use standard IPA (International Phonetic Alphabet) to fine-tune how the voice articulates."
      icon={Warning} />
  </div>

  <div class="modal--container" slot="default">
    <div class="modal--container__row">
      <div class="title--cell">Text</div>
      <div class="italic--cell">{text}</div>
    </div>
    <div class="modal--container__row">
      <div class="title--cell">IPA Code</div>
      <TextInput
        type="text"
        class="bx--text-input"
        bind:value={ph}
        placeholder="Specify IPA Code" />
      {#if ipaExists}
        <div class="button--cell">
          {#if loadingIPACode}
            <Loading small withOverlay={false} />
          {:else}
            <Button
              kind="ghost"
              size="small"
              type="button"
              disabled={text === ''}
              on:click={async () => {
                await getIpaCode();
              }}>get IPA code</Button>
          {/if}
        </div>
      {/if}
    </div>
    {#if voiceExists}
      <div class="modal--container__row">
        <div class="title--cell">Preview</div>
        <div class="action--cell">
          {#if loadingAudioText}
            <div class="audio-player">
              <Loading small withOverlay={false} />
            </div>
          {:else if audioText !== '' && ph && ph === phUsedToCalculateAudio && !loadingIPACode}
            <div class="audio-player">
              <AudioPlayer src={audioText} disabled={!ph || audioText === ''} />
            </div>
          {/if}
        </div>

        {#if audioText === '' || (ph && ph !== phUsedToCalculateAudio && !loadingAudioText && !loadingIPACode)}
          <div class="button--cell">
            <button
              class="preview--button"
              disabled={loadingAudioText || !ph || ph === '' || ph === phUsedToCalculateAudio}
              on:click={async () => {
                await getBase64Audio();
              }}>
              <Play />
            </button>
          </div>
        {/if}
      </div>
    {/if}
  </div>
</Modal>

<style lang="scss">
  .action--cell {
    display: flex;
    flex-direction: row;
    min-width: 20%;
    align-self: center;
    width: 100%;
  }
  .audio-player {
    width: 75%;
  }
  .button--cell {
    min-width: 20%;
    align-self: center;
  }
  .italic--cell {
    font-style: italic;
  }
  .modal--container {
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
    background-color: #f4f4f4;
    margin-bottom: 0.5rem;
  }
  .modal--container__row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    column-gap: 2rem;
  }
  .preview--button {
    align-self: center;
    border: none;
    background-color: transparent;
    cursor: pointer;
  }
  .title--cell {
    min-width: 20%;
    align-self: center;
  }
</style>
